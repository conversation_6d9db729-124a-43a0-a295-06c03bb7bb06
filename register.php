<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'config/security.php';

initSecurityBeforeSession();
session_start();
initSecurityAfterSession();

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

if ($_POST) {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $plan = sanitizeInput($_POST['plan'] ?? 'basic');

    // Validation
    if (empty($email) || empty($password) || empty($confirmPassword)) {
        $error = 'Please fill in all fields.';
    } elseif (!isValidEmail($email)) {
        $error = 'Please enter a valid email address.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match.';
    } elseif (getUserByEmail($email)) {
        $error = 'An account with this email already exists.';
    } else {
        // Create user
        $userId = createUser($email, $password, $plan);

        if ($userId) {
            $_SESSION['user_id'] = $userId;
            header('Location: dashboard.php?welcome=1');
            exit;
        } else {
            $error = 'Failed to create account. Please try again.';
        }
    }
}

$planLimits = getPlanLimits();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - AI API Provider</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    Create your account
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Or
                    <a href="login.php" class="font-medium text-blue-600 hover:text-blue-500">
                        sign in to existing account
                    </a>
                </p>
            </div>

            <form class="mt-8 space-y-6" method="POST">
                <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <div class="space-y-4">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
                        <input id="email" name="email" type="email" required
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               placeholder="Enter your email" value="<?php echo htmlspecialchars($email ?? ''); ?>">
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                        <input id="password" name="password" type="password" required
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               placeholder="Enter your password">
                        <p class="mt-1 text-sm text-gray-500">Must be at least 6 characters</p>
                    </div>

                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                        <input id="confirm_password" name="confirm_password" type="password" required
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                               placeholder="Confirm your password">
                    </div>

                    <div>
                        <label for="plan" class="block text-sm font-medium text-gray-700">Choose Plan</label>
                        <select id="plan" name="plan"
                                class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="basic" <?php echo ($plan ?? 'basic') === 'basic' ? 'selected' : ''; ?>>
                                Basic - Free (<?php echo formatNumber($planLimits['basic']['limit']); ?> user messages/month)
                            </option>
                            <option value="pro" <?php echo ($plan ?? '') === 'pro' ? 'selected' : ''; ?>>
                                Pro - $<?php echo $planLimits['pro']['price']; ?>/month (<?php echo formatNumber($planLimits['pro']['limit']); ?> user messages/month)
                            </option>
                            <option value="developer" <?php echo ($plan ?? '') === 'developer' ? 'selected' : ''; ?>>
                                Developer - $<?php echo $planLimits['developer']['price']; ?>/month (<?php echo formatNumber($planLimits['developer']['limit']); ?> user messages/month)
                            </option>
                            <option value="enterprise" <?php echo ($plan ?? '') === 'enterprise' ? 'selected' : ''; ?>>
                                Enterprise - $<?php echo $planLimits['enterprise']['price']; ?>/month (<?php echo formatNumber($planLimits['enterprise']['limit']); ?> user messages/month)
                            </option>
                        </select>
                        <p class="mt-1 text-sm text-gray-500">You can upgrade your plan later</p>
                    </div>
                </div>

                <div class="flex items-center">
                    <input id="terms" name="terms" type="checkbox" required
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="terms" class="ml-2 block text-sm text-gray-900">
                        I agree to the <a href="#" class="text-blue-600 hover:text-blue-500">Terms of Service</a>
                        and <a href="#" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>
                    </label>
                </div>

                <div>
                    <button type="submit"
                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Create Account
                    </button>
                </div>

                <div class="text-center">
                    <a href="index.php" class="font-medium text-gray-600 hover:text-gray-500">
                        ← Back to home
                    </a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
