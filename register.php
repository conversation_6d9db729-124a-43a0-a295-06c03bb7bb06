<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'config/security.php';

initSecurityBeforeSession();
session_start();
initSecurityAfterSession();

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

if ($_POST) {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $plan = sanitizeInput($_POST['plan'] ?? 'basic');

    // Validation
    if (empty($email) || empty($password) || empty($confirmPassword)) {
        $error = 'Please fill in all fields.';
    } elseif (!isValidEmail($email)) {
        $error = 'Please enter a valid email address.';
    } elseif (strlen($password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } elseif ($password !== $confirmPassword) {
        $error = 'Passwords do not match.';
    } elseif (getUserByEmail($email)) {
        $error = 'An account with this email already exists.';
    } else {
        // Create user
        $userId = createUser($email, $password, $plan);

        if ($userId) {
            $_SESSION['user_id'] = $userId;
            header('Location: dashboard.php?welcome=1');
            exit;
        } else {
            $error = 'Failed to create account. Please try again.';
        }
    }
}

$planLimits = getPlanLimits();

// Set page variables for header
$pageTitle = 'Register - AI API Provider';
$pageHeader = false; // No page header for auth pages

// Include header
require_once 'includes/header.php';
?>

<!-- Additional styles for auth pages -->
<style>
    .auth-card {
        background: #252a3d;
        border: 1px solid #2d3748;
        border-radius: 12px;
        padding: 32px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
    }
    .auth-input {
        background: #1a1d29;
        border: 1px solid #2d3748;
        color: #e2e8f0;
    }
    .auth-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    .auth-input::placeholder {
        color: #a0aec0;
    }
    .auth-select {
        background: #1a1d29;
        border: 1px solid #2d3748;
        color: #e2e8f0;
    }
    .auth-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    .auth-select option {
        background: #1a1d29;
        color: #e2e8f0;
    }
</style>

<div class="min-h-screen flex items-center justify-center padding-responsive">
    <div class="container-responsive max-w-md w-full space-responsive">
        <div class="text-center">
            <h1 class="text-responsive-xl font-bold gradient-text mb-2">AI API Provider</h1>
            <h2 class="text-responsive-xl font-bold text-light mb-2">
                Create your account
            </h2>
            <p class="text-responsive-sm text-muted">
                Or
                <a href="login.php" class="font-medium text-blue-400 hover:text-blue-300 transition-colors">
                    sign in to existing account
                </a>
            </p>
        </div>

        <div class="auth-card card-responsive">
            <form class="form-responsive space-y-6" method="POST">
                <?php if ($error): ?>
                    <div class="bg-red-900 bg-opacity-20 border border-red-500 text-red-400 px-4 py-3 rounded-lg text-sm">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="bg-green-900 bg-opacity-20 border border-green-500 text-green-400 px-4 py-3 rounded-lg text-sm">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <div class="space-y-4">
                    <div>
                        <label for="email" class="block text-sm font-medium text-light mb-1">Email address</label>
                        <input id="email" name="email" type="email" required
                               class="auth-input w-full px-3 py-2.5 rounded-lg text-sm focus:outline-none transition-all"
                               placeholder="Enter your email" value="<?php echo htmlspecialchars($email ?? ''); ?>">
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-light mb-1">Password</label>
                        <input id="password" name="password" type="password" required
                               class="auth-input w-full px-3 py-2.5 rounded-lg text-sm focus:outline-none transition-all"
                               placeholder="Enter your password">
                        <p class="mt-1 text-sm text-muted">Must be at least 6 characters</p>
                    </div>

                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-light mb-1">Confirm Password</label>
                        <input id="confirm_password" name="confirm_password" type="password" required
                               class="auth-input w-full px-3 py-2.5 rounded-lg text-sm focus:outline-none transition-all"
                               placeholder="Confirm your password">
                    </div>

                    <div>
                        <label for="plan" class="block text-sm font-medium text-light mb-1">Choose Plan</label>
                        <select id="plan" name="plan"
                                class="auth-select w-full px-3 py-2.5 rounded-lg text-sm focus:outline-none transition-all">
                            <option value="basic" <?php echo ($plan ?? 'basic') === 'basic' ? 'selected' : ''; ?>>
                                Basic - Free (<?php echo formatNumber($planLimits['basic']['limit']); ?> user messages/month)
                            </option>
                            <option value="pro" <?php echo ($plan ?? '') === 'pro' ? 'selected' : ''; ?>>
                                Pro - $<?php echo $planLimits['pro']['price']; ?>/month (<?php echo formatNumber($planLimits['pro']['limit']); ?> user messages/month)
                            </option>
                            <option value="developer" <?php echo ($plan ?? '') === 'developer' ? 'selected' : ''; ?>>
                                Developer - $<?php echo $planLimits['developer']['price']; ?>/month (<?php echo formatNumber($planLimits['developer']['limit']); ?> user messages/month)
                            </option>
                            <option value="enterprise" <?php echo ($plan ?? '') === 'enterprise' ? 'selected' : ''; ?>>
                                Enterprise - $<?php echo $planLimits['enterprise']['price']; ?>/month (<?php echo formatNumber($planLimits['enterprise']['limit']); ?> user messages/month)
                            </option>
                        </select>
                        <p class="mt-1 text-sm text-muted">You can upgrade your plan later</p>
                    </div>
                </div>

                <div class="flex items-center">
                    <input id="terms" name="terms" type="checkbox" required
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 rounded bg-gray-700">
                    <label for="terms" class="ml-2 block text-sm text-muted">
                        I agree to the <a href="#" class="text-blue-400 hover:text-blue-300">Terms of Service</a>
                        and <a href="#" class="text-blue-400 hover:text-blue-300">Privacy Policy</a>
                    </label>
                </div>

                <div>
                    <button type="submit"
                            class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-2.5 px-4 rounded-lg text-sm font-medium hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-opacity">
                        Create Account
                    </button>
                </div>

                <div class="text-center">
                    <a href="index.php" class="text-sm font-medium text-muted hover:text-light transition-colors">
                        ← Back to home
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
