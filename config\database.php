<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'openai_api_provider');

// Create connection
function getConnection() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch(PDOException $e) {
        die("Connection failed: " . $e->getMessage());
    }
}

// Initialize database and tables
function initializeDatabase() {
    try {
        // First, create database if it doesn't exist
        $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME;
        $pdo->exec($sql);
        
        // Now connect to the database
        $pdo = getConnection();
        
        // Create users table
        $sql = "CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            plan ENUM('basic', 'pro', 'developer', 'enterprise') DEFAULT 'basic',
            api_key VARCHAR(255) UNIQUE NOT NULL,
            requests_used INT DEFAULT 0,
            requests_limit INT DEFAULT 100,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $pdo->exec($sql);
        
        // Create api_requests table for logging
        $sql = "CREATE TABLE IF NOT EXISTS api_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            model_requested VARCHAR(100) NOT NULL,
            model_actual VARCHAR(100) NOT NULL,
            tokens_used INT DEFAULT 0,
            response_time FLOAT DEFAULT 0,
            status_code INT DEFAULT 200,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )";
        $pdo->exec($sql);
        
        // Create payments table
        $sql = "CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            plan VARCHAR(50) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
            payment_method VARCHAR(50),
            transaction_id VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )";
        $pdo->exec($sql);
        
        return true;
    } catch(PDOException $e) {
        error_log("Database initialization failed: " . $e->getMessage());
        return false;
    }
}

// Model mapping configuration
function getModelMapping() {
    return [
        'claude-3.5-sonnet' => 'claude-3-5-sonnet-20241022',
        'claude-3.7-sonnet' => 'claude-3-7-sonnet-20250219',
        'claude-4-sonnet' => 'anthropic/claude-sonnet-4',
        'qwen-2.5-72b-instruct-turbo' => 'Qwen/Qwen2.5-72B-Instruct-Turbo',
        'qwen-2.5-coder-32b-instruct' => 'Qwen/Qwen2.5-Coder-32B-Instruct',
        'llama-4-maverick-17b' => 'llama-4-maverick-17b-128e-instruct-fp8',
        'deepseek-r1' => 'deepseek-r1',
        'deepseek-chat' => 'deepseek/deepseek-chat',
        'deepseek-v3' => 'deepseek-v3',
        'o1' => 'o1',
        'o3-mini' => 'o3-mini',
        'gpt-4o-mini' => 'gpt-4o-mini',
        'o4-mini' => 'o4-mini',
        'grok-3-beta' => 'grok-3-beta',
        'gemini-2.5-pro' => 'gemini-2.5-pro-preview-03-25'
    ];
}

// Plan limits configuration
function getPlanLimits() {
    return [
        'basic' => ['limit' => 100, 'price' => 0],
        'pro' => ['limit' => 500, 'price' => 20],
        'developer' => ['limit' => 2500, 'price' => 50],
        'enterprise' => ['limit' => 10000, 'price' => 150]
    ];
}

// Initialize database on first load
if (!file_exists(__DIR__ . '/.db_initialized')) {
    if (initializeDatabase()) {
        file_put_contents(__DIR__ . '/.db_initialized', 'true');
    }
}
?>
