<?php
// Security configuration and functions

// CSRF Protection
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Rate limiting for login attempts
function checkLoginAttempts($email) {
    $key = 'login_attempts_' . md5($email);
    $attempts = $_SESSION[$key] ?? 0;
    $last_attempt = $_SESSION[$key . '_time'] ?? 0;

    // Reset attempts after 15 minutes
    if (time() - $last_attempt > 900) {
        unset($_SESSION[$key]);
        unset($_SESSION[$key . '_time']);
        return true;
    }

    return $attempts < 5;
}

function recordLoginAttempt($email, $success = false) {
    $key = 'login_attempts_' . md5($email);

    if ($success) {
        unset($_SESSION[$key]);
        unset($_SESSION[$key . '_time']);
    } else {
        $_SESSION[$key] = ($_SESSION[$key] ?? 0) + 1;
        $_SESSION[$key . '_time'] = time();
    }
}

// Input validation
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) && strlen($email) <= 255;
}

function validatePassword($password) {
    return strlen($password) >= 6 && strlen($password) <= 255;
}

function validatePlan($plan) {
    $validPlans = ['basic', 'pro', 'developer', 'enterprise'];
    return in_array($plan, $validPlans);
}

// XSS Protection
function escapeOutput($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

// SQL Injection Protection (already handled by PDO, but additional validation)
function validateApiKey($apiKey) {
    return preg_match('/^sk-[a-f0-9]{48}$/', $apiKey);
}

// Session Security
function secureSession() {
    // Only set ini settings if session is not active
    if (session_status() === PHP_SESSION_NONE) {
        // Set secure session parameters before starting session
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    }

    // Regenerate session ID periodically (only if session is active)
    if (session_status() === PHP_SESSION_ACTIVE) {
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) {
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
    }
}

// API Rate Limiting
function checkApiRateLimit($userId, $timeWindow = 60, $maxRequests = 10) {
    $key = 'api_rate_' . $userId;
    $now = time();

    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = [];
    }

    // Remove old requests outside time window
    $_SESSION[$key] = array_filter($_SESSION[$key], function($timestamp) use ($now, $timeWindow) {
        return ($now - $timestamp) < $timeWindow;
    });

    // Check if under limit
    if (count($_SESSION[$key]) >= $maxRequests) {
        return false;
    }

    // Record this request
    $_SESSION[$key][] = $now;
    return true;
}

// Content Security Policy
function setSecurityHeaders() {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' cdn.jsdelivr.net cdnjs.cloudflare.com; style-src \'self\' \'unsafe-inline\' cdn.jsdelivr.net cdnjs.cloudflare.com; img-src \'self\' data:; font-src \'self\' cdnjs.cloudflare.com');
}

// Logging security events
function logSecurityEvent($event, $details = []) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $event,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'details' => $details
    ];

    $logFile = __DIR__ . '/../logs/security.log';
    $logDir = dirname($logFile);

    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
}

// Check for suspicious activity
function detectSuspiciousActivity() {
    $suspiciousPatterns = [
        'sql_injection' => '/(\bunion\b|\bselect\b|\binsert\b|\bdelete\b|\bdrop\b|\bupdate\b)/i',
        'xss_attempt' => '/(<script|javascript:|onload=|onerror=)/i',
        'path_traversal' => '/(\.\.|\/etc\/|\/var\/|\/usr\/)/i'
    ];

    $input = $_SERVER['REQUEST_URI'] . ' ' . file_get_contents('php://input');

    foreach ($suspiciousPatterns as $type => $pattern) {
        if (preg_match($pattern, $input)) {
            logSecurityEvent('suspicious_activity', [
                'type' => $type,
                'input' => substr($input, 0, 500)
            ]);
            return true;
        }
    }

    return false;
}

// Initialize security (call before session_start)
function initSecurityBeforeSession() {
    // Set secure session parameters before starting session
    if (session_status() === PHP_SESSION_NONE) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    }

    setSecurityHeaders();

    if (detectSuspiciousActivity()) {
        http_response_code(403);
        exit('Access denied');
    }
}

// Initialize security (call after session_start)
function initSecurityAfterSession() {
    secureSession();
}

// Password strength checker
function checkPasswordStrength($password) {
    $score = 0;
    $feedback = [];

    if (strlen($password) >= 8) $score++;
    else $feedback[] = 'Use at least 8 characters';

    if (preg_match('/[a-z]/', $password)) $score++;
    else $feedback[] = 'Include lowercase letters';

    if (preg_match('/[A-Z]/', $password)) $score++;
    else $feedback[] = 'Include uppercase letters';

    if (preg_match('/[0-9]/', $password)) $score++;
    else $feedback[] = 'Include numbers';

    if (preg_match('/[^a-zA-Z0-9]/', $password)) $score++;
    else $feedback[] = 'Include special characters';

    return [
        'score' => $score,
        'strength' => $score < 2 ? 'weak' : ($score < 4 ? 'medium' : 'strong'),
        'feedback' => $feedback
    ];
}

// API Key security
function rotateApiKey($userId) {
    $newApiKey = generateApiKey();
    $pdo = getConnection();
    $stmt = $pdo->prepare("UPDATE users SET api_key = ? WHERE id = ?");

    if ($stmt->execute([$newApiKey, $userId])) {
        logSecurityEvent('api_key_rotated', ['user_id' => $userId]);
        return $newApiKey;
    }

    return false;
}

// Check for compromised API keys (basic implementation)
function checkApiKeyCompromise($apiKey) {
    // In a real implementation, you might check against a database of compromised keys
    // or implement additional security measures

    $suspiciousPatterns = [
        '/^sk-test/',  // Test keys shouldn't be used in production
        '/^sk-demo/',  // Demo keys
    ];

    foreach ($suspiciousPatterns as $pattern) {
        if (preg_match($pattern, $apiKey)) {
            return true;
        }
    }

    return false;
}
?>
