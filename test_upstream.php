<?php
// Test script for upstream API connection
echo "<h1>Upstream API Test</h1>";

$upstreamUrl = 'https://oi-vscode-server-5.onrender.com/v1/chat/completions';

$testData = [
    'model' => 'claude-3-5-sonnet-20241022',
    'messages' => [
        [
            'role' => 'user',
            'content' => 'Hello, this is a test message. Please respond briefly.'
        ]
    ],
    'max_tokens' => 50
];

echo "<h2>Testing Direct Connection to Upstream API</h2>";
echo "URL: $upstreamUrl<br>";
echo "Test data: <pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>";

$startTime = microtime(true);

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $upstreamUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($testData),
    CURLOPT_HTTPHEADER => [
        'Accept: application/json',
        'Content-Type: application/json',
        'User-Agent: Pr/JS 4.73.1',
        'X-Stainless-Lang: js',
        'X-Stainless-Package-Version: 4.73.1',
        'X-Stainless-OS: Windows',
        'X-Stainless-Arch: x64',
        'X-Stainless-Runtime: node',
        'X-Stainless-Runtime-Version: v20.18.2',
        'Authorization: Bearer xxx',
        'CustomerID: cus_SBLPR2vIB7Tp4z',
        'X-Stainless-Retry-Count: 0',
        'Connection: close',
        'Accept-Encoding: gzip, deflate'
    ],
    CURLOPT_TIMEOUT => 60,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_ENCODING => 'gzip, deflate',
    CURLOPT_VERBOSE => true
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
$responseTime = microtime(true) - $startTime;

curl_close($ch);

echo "<h2>Results</h2>";
echo "HTTP Status Code: $httpCode<br>";
echo "Response Time: " . round($responseTime, 3) . " seconds<br>";

if ($error) {
    echo "❌ cURL Error: $error<br>";
} else {
    echo "✅ Request completed successfully<br>";
}

echo "<h3>Response Body:</h3>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

// Try to parse JSON response
$responseData = json_decode($response, true);
if ($responseData) {
    echo "<h3>Parsed Response:</h3>";
    echo "<pre>" . json_encode($responseData, JSON_PRETTY_PRINT) . "</pre>";
    
    if (isset($responseData['choices'][0]['message']['content'])) {
        echo "<h3>AI Response:</h3>";
        echo "<blockquote>" . htmlspecialchars($responseData['choices'][0]['message']['content']) . "</blockquote>";
    }
} else {
    echo "❌ Failed to parse JSON response<br>";
}

echo "<p><a href='index.php'>← Back to Home</a></p>";
?>
