<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'config/security.php';

initSecurityBeforeSession();
session_start();
initSecurityAfterSession();

requireLogin();

$user = getUserById($_SESSION['user_id']);
$stats = getUserStats($_SESSION['user_id']);
$planLimits = getPlanLimits();
$modelMapping = getModelMapping();

$welcome = isset($_GET['welcome']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - AI API Provider</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1d29;
            color: #e2e8f0;
        }
        .dark-bg {
            background: #1a1d29;
        }
        .sidebar {
            background: #252a3d;
            border-right: 1px solid #2d3748;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
        }
        .main-content {
            background: #1a1d29;
            min-height: 100vh;
        }
        .card {
            background: #252a3d;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            border: 1px solid #2d3748;
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            transform: translateY(-2px);
        }
        .stat-card {
            background: #252a3d;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #2d3748;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .sidebar-link {
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 4px 0;
            color: #a0aec0;
        }
        .sidebar-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #e2e8f0;
            transform: translateX(4px);
        }
        .sidebar-link.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 6px;
            height: 8px;
        }
        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .badge-success { background: rgba(16, 185, 129, 0.2); color: #10b981; border: 1px solid #10b981; }
        .badge-warning { background: rgba(245, 158, 11, 0.2); color: #f59e0b; border: 1px solid #f59e0b; }
        .badge-info { background: rgba(59, 130, 246, 0.2); color: #3b82f6; border: 1px solid #3b82f6; }
        .badge-purple { background: rgba(139, 92, 246, 0.2); color: #8b5cf6; border: 1px solid #8b5cf6; }
        .table-row {
            transition: all 0.2s ease;
        }
        .table-row:hover {
            background: rgba(102, 126, 234, 0.1);
        }
        .chart-container {
            background: #252a3d;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #2d3748;
        }
        .mini-chart {
            width: 60px;
            height: 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        .mini-chart::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%);
        }
        .header-dark {
            background: #252a3d;
            border-bottom: 1px solid #2d3748;
        }
        .text-light { color: #e2e8f0; }
        .text-muted { color: #a0aec0; }
        .text-success { color: #10b981; }
        .text-warning { color: #f59e0b; }
        .text-info { color: #3b82f6; }
        .bg-dark-card { background: #252a3d; }
        .border-dark { border-color: #2d3748; }
    </style>
</head>
<body>
    <div class="flex h-screen dark-bg">
        <!-- Sidebar -->
        <div class="sidebar w-64 flex-shrink-0">
            <div class="flex flex-col h-full">
                <!-- Logo -->
                <div class="flex items-center justify-center h-16 px-4 border-b border-gray-600">
                    <h1 class="text-xl font-bold text-light">AI API Provider</h1>
                </div>

                <!-- Navigation -->
                <nav class="flex-1 px-4 py-6 space-y-2">
                    <a href="dashboard.php" class="sidebar-link active flex items-center px-4 py-3 text-sm font-medium">
                        <i class="fas fa-tachometer-alt mr-3 w-5"></i>
                        Dashboard
                    </a>
                    <a href="docs.php" class="sidebar-link flex items-center px-4 py-3 text-sm font-medium">
                        <i class="fas fa-book mr-3 w-5"></i>
                        API Documentation
                    </a>
                    <a href="#" class="sidebar-link flex items-center px-4 py-3 text-sm font-medium">
                        <i class="fas fa-chart-bar mr-3 w-5"></i>
                        Analytics
                    </a>
                    <a href="#" class="sidebar-link flex items-center px-4 py-3 text-sm font-medium">
                        <i class="fas fa-cog mr-3 w-5"></i>
                        Settings
                    </a>
                    <a href="#" class="sidebar-link flex items-center px-4 py-3 text-sm font-medium">
                        <i class="fas fa-credit-card mr-3 w-5"></i>
                        Billing
                    </a>
                </nav>

                <!-- User Info -->
                <div class="px-4 py-4 border-t border-gray-600">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-light truncate">
                                <?php echo htmlspecialchars($user['email']); ?>
                            </p>
                            <p class="text-xs text-muted">
                                <?php echo ucfirst($user['plan']); ?> Plan
                            </p>
                        </div>
                        <a href="logout.php" class="text-muted hover:text-light transition-colors">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Header -->
            <header class="header-dark shadow-sm">
                <div class="flex items-center justify-between px-6 py-4">
                    <div>
                        <h1 class="text-2xl font-semibold text-light">Dashboard</h1>
                        <p class="text-sm text-muted">Welcome back! Here's what's happening with your API usage.</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="p-2 text-muted hover:text-light rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-bell"></i>
                        </button>
                        <button class="p-2 text-muted hover:text-light rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 overflow-y-auto dark-bg">
                <div class="p-6">
                    <?php if ($welcome): ?>
                        <div class="bg-green-900 bg-opacity-20 border border-green-500 text-green-400 px-4 py-3 rounded-lg mb-6 flex items-center">
                            <i class="fas fa-check-circle mr-3 text-green-400"></i>
                            <div>
                                <strong>Welcome!</strong> Your account has been created successfully. Your API key is ready to use.
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="stat-card">
                            <div class="flex items-start justify-between mb-4">
                                <div>
                                    <p class="text-sm font-medium text-muted mb-1">Total Users</p>
                                    <p class="text-2xl font-bold text-light"><?php echo formatNumber($user['requests_used']); ?></p>
                                    <p class="text-xs text-success mt-1 flex items-center">
                                        <i class="fas fa-arrow-up mr-1"></i>
                                        +12% from last month
                                    </p>
                                </div>
                                <div class="mini-chart"></div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="flex items-start justify-between mb-4">
                                <div>
                                    <p class="text-sm font-medium text-muted mb-1">Total Profit</p>
                                    <p class="text-2xl font-bold text-light"><?php echo formatNumber($user['requests_limit']); ?></p>
                                    <p class="text-xs text-success mt-1 flex items-center">
                                        <i class="fas fa-arrow-up mr-1"></i>
                                        +8% from last month
                                    </p>
                                </div>
                                <div class="mini-chart" style="background: linear-gradient(45deg, #ec4899, #f97316);"></div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="flex items-start justify-between mb-4">
                                <div>
                                    <p class="text-sm font-medium text-muted mb-1">Total Expenses</p>
                                    <p class="text-2xl font-bold text-light">$<?php echo number_format($stats['monthly_tokens'] / 1000, 2); ?>K</p>
                                    <p class="text-xs text-success mt-1 flex items-center">
                                        <i class="fas fa-arrow-up mr-1"></i>
                                        +15% from last month
                                    </p>
                                </div>
                                <div class="mini-chart" style="background: linear-gradient(45deg, #10b981, #059669);"></div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="flex items-start justify-between mb-4">
                                <div>
                                    <p class="text-sm font-medium text-muted mb-1">Total Cost</p>
                                    <p class="text-2xl font-bold text-light">$<?php echo number_format(getRemainingRequests($user) / 100, 2); ?>K</p>
                                    <p class="text-xs text-warning mt-1 flex items-center">
                                        <i class="fas fa-arrow-down mr-1"></i>
                                        -3% from last month
                                    </p>
                                </div>
                                <div class="mini-chart" style="background: linear-gradient(45deg, #f59e0b, #d97706);"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Usage Progress Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Usage Chart -->
                        <div class="lg:col-span-2 chart-container">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-lg font-semibold text-light">Monthly Usage Overview</h3>
                                <span class="badge badge-info">This Month</span>
                            </div>

                            <div class="mb-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-muted">Messages Used</span>
                                    <span class="text-sm font-semibold text-light">
                                        <?php echo formatNumber($user['requests_used']); ?> / <?php echo formatNumber($user['requests_limit']); ?>
                                    </span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-3">
                                    <div class="progress-bar h-3 rounded-full transition-all duration-500"
                                         style="width: <?php echo calculatePercentage($user['requests_used'], $user['requests_limit']); ?>%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-muted mt-1">
                                    <span>0</span>
                                    <span><?php echo calculatePercentage($user['requests_used'], $user['requests_limit']); ?>% used</span>
                                    <span><?php echo formatNumber($user['requests_limit']); ?></span>
                                </div>
                            </div>

                            <?php if (needsPlanUpgrade($user)): ?>
                                <div class="mt-4 p-4 bg-red-900 bg-opacity-20 border border-red-500 rounded-lg flex items-start">
                                    <i class="fas fa-exclamation-triangle text-red-400 mr-3 mt-0.5"></i>
                                    <div>
                                        <p class="text-red-400 font-medium">Limit Reached</p>
                                        <p class="text-red-300 text-sm">You've reached your monthly message limit. <a href="#upgrade" class="font-semibold underline">Upgrade your plan</a> to continue using the API.</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Quick Stats -->
                        <div class="chart-container">
                            <h3 class="text-lg font-semibold text-light mb-4">Quick Stats</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-muted">Total Requests</span>
                                    <span class="font-semibold text-light"><?php echo formatNumber($stats['monthly_requests']); ?></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-muted">Tokens Used</span>
                                    <span class="font-semibold text-light"><?php echo formatNumber($stats['monthly_tokens']); ?></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-muted">Most Used Model</span>
                                    <span class="font-semibold text-xs text-light"><?php echo $stats['most_used_model']; ?></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-muted">Plan Status</span>
                                    <span class="badge badge-success">Active</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bottom Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- API Key Section -->
                        <div class="chart-container">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-light">API Configuration</h3>
                                <i class="fas fa-key text-muted"></i>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-muted mb-2">Your API Key</label>
                                    <div class="flex">
                                        <input type="password" id="apiKey" value="<?php echo htmlspecialchars($user['api_key']); ?>"
                                               class="flex-1 px-3 py-2 border border-gray-600 rounded-l-lg bg-gray-700 text-light text-sm font-mono" readonly>
                                        <button onclick="toggleApiKey()"
                                                class="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-r-lg hover:opacity-90 transition-opacity">
                                            <i class="fas fa-eye" id="eyeIcon"></i>
                                        </button>
                                    </div>
                                    <p class="text-xs text-muted mt-2 flex items-center">
                                        <i class="fas fa-shield-alt mr-1"></i>
                                        Keep your API key secure and don't share it publicly.
                                    </p>
                                </div>

                                <div>
                                    <h4 class="font-medium text-light mb-2 flex items-center">
                                        <i class="fas fa-code mr-2 text-muted"></i>
                                        Quick Start
                                    </h4>
                                    <div class="bg-gray-800 p-4 rounded-lg text-sm overflow-x-auto border border-gray-600">
                                        <code class="text-green-400">curl -X POST http://<?php echo $_SERVER['HTTP_HOST']; ?>/api/v1/chat/completions \<br>
<span class="text-blue-400">  -H</span> <span class="text-yellow-300">"Authorization: Bearer <?php echo substr($user['api_key'], 0, 20); ?>..."</span> \<br>
<span class="text-blue-400">  -H</span> <span class="text-yellow-300">"Content-Type: application/json"</span> \<br>
<span class="text-blue-400">  -d</span> <span class="text-yellow-300">'{"model": "claude-3.5-sonnet", "messages": [{"role": "user", "content": "Hello!"}]}'</span></code>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Plan Upgrade Section -->
                        <div class="chart-container" id="upgrade">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-light">Upgrade Plan</h3>
                                <i class="fas fa-rocket text-muted"></i>
                            </div>

                            <div class="space-y-3">
                                <?php foreach ($planLimits as $planName => $planData): ?>
                                    <?php if ($planName === $user['plan']) continue; ?>
                                    <div class="border border-gray-600 rounded-lg p-4 hover:border-blue-500 transition-colors <?php echo $planName === 'pro' ? 'border-blue-500 bg-blue-900 bg-opacity-20' : 'bg-gray-700 bg-opacity-30'; ?>">
                                        <div class="flex justify-between items-center">
                                            <div>
                                                <h4 class="font-semibold text-light flex items-center">
                                                    <?php echo ucfirst($planName); ?>
                                                    <?php if ($planName === 'pro'): ?>
                                                        <span class="ml-2 badge badge-info">Popular</span>
                                                    <?php endif; ?>
                                                </h4>
                                                <p class="text-sm text-muted">
                                                    <?php echo formatNumber($planData['limit']); ?> user messages/month
                                                    <?php if ($planData['price'] > 0): ?>
                                                        - $<?php echo $planData['price']; ?>/month
                                                    <?php else: ?>
                                                        - Free
                                                    <?php endif; ?>
                                                </p>
                                            </div>
                                            <button class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-lg hover:opacity-90 text-sm font-medium transition-opacity">
                                                <?php echo $planData['price'] > 0 ? 'Upgrade' : 'Downgrade'; ?>
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Available Models -->
                    <div class="chart-container mt-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-light">Available AI Models</h3>
                            <span class="badge badge-success"><?php echo count($modelMapping); ?> Models</span>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <?php foreach ($modelMapping as $displayName => $actualName): ?>
                                <div class="border border-gray-600 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-900 hover:bg-opacity-20 transition-all">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h4 class="font-medium text-light text-sm"><?php echo htmlspecialchars($displayName); ?></h4>
                                            <p class="text-xs text-muted mt-1 font-mono"><?php echo htmlspecialchars($actualName); ?></p>
                                        </div>
                                        <div class="w-2 h-2 bg-green-400 rounded-full ml-2 mt-1"></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        function toggleApiKey() {
            const apiKeyInput = document.getElementById('apiKey');
            const eyeIcon = document.getElementById('eyeIcon');

            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
                eyeIcon.className = 'fas fa-eye-slash';
            } else {
                apiKeyInput.type = 'password';
                eyeIcon.className = 'fas fa-eye';
            }
        }
    </script>
</body>
</html>
