<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'config/security.php';

initSecurityBeforeSession();
session_start();
initSecurityAfterSession();

requireLogin();

$user = getUserById($_SESSION['user_id']);
$stats = getUserStats($_SESSION['user_id']);
$planLimits = getPlanLimits();
$modelMapping = getModelMapping();

$welcome = isset($_GET['welcome']);

// Set page variables for header
$pageTitle = 'Dashboard';
$pageHeader = true;
$pageDescription = 'Welcome back! Here\'s what\'s happening with your API usage.';

// Include header
require_once 'includes/header.php';
?>
                    <?php if ($welcome): ?>
                        <div class="bg-green-900 bg-opacity-20 border border-green-500 text-green-400 px-4 py-3 rounded-lg mb-6 flex items-center">
                            <i class="fas fa-check-circle mr-3 text-green-400"></i>
                            <div>
                                <strong>Welcome!</strong> Your account has been created successfully. Your API key is ready to use.
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="stat-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-muted mb-1">Current Plan</p>
                                    <p class="text-2xl font-bold gradient-text"><?php echo ucfirst($user['plan']); ?></p>
                                    <p class="text-xs text-muted mt-1">
                                        <?php
                                        $planLimits = getPlanLimits();
                                        echo $planLimits[$user['plan']]['price'] > 0 ? '$' . $planLimits[$user['plan']]['price'] . '/month' : 'Free';
                                        ?>
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-crown text-white"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-muted mb-1">Messages Used</p>
                                    <p class="text-2xl font-bold text-light">
                                        <?php echo formatNumber($user['requests_used']); ?>
                                    </p>
                                    <p class="text-xs text-muted mt-1">
                                        of <?php echo formatNumber($user['requests_limit']); ?> total
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-comments text-white"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-muted mb-1">Usage</p>
                                    <p class="text-2xl font-bold text-light">
                                        <?php echo calculatePercentage($user['requests_used'], $user['requests_limit']); ?>%
                                    </p>
                                    <p class="text-xs text-muted mt-1">This month</p>
                                </div>
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-chart-pie text-white"></i>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-muted mb-1">Remaining</p>
                                    <p class="text-2xl font-bold text-light">
                                        <?php echo formatNumber(getRemainingRequests($user)); ?>
                                    </p>
                                    <p class="text-xs text-muted mt-1">Messages left</p>
                                </div>
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-battery-three-quarters text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Usage Progress Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Usage Chart -->
                        <div class="lg:col-span-2 chart-container">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-lg font-semibold text-light">Monthly Usage Overview</h3>
                                <span class="badge badge-info">This Month</span>
                            </div>

                            <div class="mb-4">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-muted">Messages Used</span>
                                    <span class="text-sm font-semibold text-light">
                                        <?php echo formatNumber($user['requests_used']); ?> / <?php echo formatNumber($user['requests_limit']); ?>
                                    </span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-3">
                                    <div class="progress-bar h-3 rounded-full transition-all duration-500"
                                         style="width: <?php echo calculatePercentage($user['requests_used'], $user['requests_limit']); ?>%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-muted mt-1">
                                    <span>0</span>
                                    <span><?php echo calculatePercentage($user['requests_used'], $user['requests_limit']); ?>% used</span>
                                    <span><?php echo formatNumber($user['requests_limit']); ?></span>
                                </div>
                            </div>

                            <?php if (needsPlanUpgrade($user)): ?>
                                <div class="mt-4 p-4 bg-red-900 bg-opacity-20 border border-red-500 rounded-lg flex items-start">
                                    <i class="fas fa-exclamation-triangle text-red-400 mr-3 mt-0.5"></i>
                                    <div>
                                        <p class="text-red-400 font-medium">Limit Reached</p>
                                        <p class="text-red-300 text-sm">You've reached your monthly message limit. <a href="#upgrade" class="font-semibold underline">Upgrade your plan</a> to continue using the API.</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Quick Stats -->
                        <div class="chart-container">
                            <h3 class="text-lg font-semibold text-light mb-4">Quick Stats</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-muted">Total Requests</span>
                                    <span class="font-semibold text-light"><?php echo formatNumber($stats['monthly_requests']); ?></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-muted">Tokens Used</span>
                                    <span class="font-semibold text-light"><?php echo formatNumber($stats['monthly_tokens']); ?></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-muted">Most Used Model</span>
                                    <span class="font-semibold text-xs text-light"><?php echo $stats['most_used_model']; ?></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-muted">Plan Status</span>
                                    <span class="badge badge-success">Active</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bottom Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- API Key Section -->
                        <div class="chart-container">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-light">API Configuration</h3>
                                <i class="fas fa-key text-muted"></i>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-muted mb-2">Your API Key</label>
                                    <div class="flex">
                                        <input type="password" id="apiKey" value="<?php echo htmlspecialchars($user['api_key']); ?>"
                                               class="flex-1 px-3 py-2 border border-gray-600 rounded-l-lg bg-gray-700 text-light text-sm font-mono" readonly>
                                        <button onclick="toggleApiKey()"
                                                class="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-r-lg hover:opacity-90 transition-opacity">
                                            <i class="fas fa-eye" id="eyeIcon"></i>
                                        </button>
                                    </div>
                                    <p class="text-xs text-muted mt-2 flex items-center">
                                        <i class="fas fa-shield-alt mr-1"></i>
                                        Keep your API key secure and don't share it publicly.
                                    </p>
                                </div>

                                <div>
                                    <h4 class="font-medium text-light mb-2 flex items-center">
                                        <i class="fas fa-code mr-2 text-muted"></i>
                                        Quick Start
                                    </h4>
                                    <div class="bg-gray-800 p-4 rounded-lg text-sm overflow-x-auto border border-gray-600">
                                        <code class="text-green-400">curl -X POST http://<?php echo $_SERVER['HTTP_HOST']; ?>/api/v1/chat/completions \<br>
<span class="text-blue-400">  -H</span> <span class="text-yellow-300">"Authorization: Bearer <?php echo substr($user['api_key'], 0, 20); ?>..."</span> \<br>
<span class="text-blue-400">  -H</span> <span class="text-yellow-300">"Content-Type: application/json"</span> \<br>
<span class="text-blue-400">  -d</span> <span class="text-yellow-300">'{"model": "claude-3.5-sonnet", "messages": [{"role": "user", "content": "Hello!"}]}'</span></code>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Plan Upgrade Section -->
                        <div class="chart-container" id="upgrade">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-light">Upgrade Plan</h3>
                                <i class="fas fa-rocket text-muted"></i>
                            </div>

                            <div class="space-y-3">
                                <?php foreach ($planLimits as $planName => $planData): ?>
                                    <?php if ($planName === $user['plan']) continue; ?>
                                    <div class="border border-gray-600 rounded-lg p-4 hover:border-blue-500 transition-colors <?php echo $planName === 'pro' ? 'border-blue-500 bg-blue-900 bg-opacity-20' : 'bg-gray-700 bg-opacity-30'; ?>">
                                        <div class="flex justify-between items-center">
                                            <div>
                                                <h4 class="font-semibold text-light flex items-center">
                                                    <?php echo ucfirst($planName); ?>
                                                    <?php if ($planName === 'pro'): ?>
                                                        <span class="ml-2 badge badge-info">Popular</span>
                                                    <?php endif; ?>
                                                </h4>
                                                <p class="text-sm text-muted">
                                                    <?php echo formatNumber($planData['limit']); ?> user messages/month
                                                    <?php if ($planData['price'] > 0): ?>
                                                        - $<?php echo $planData['price']; ?>/month
                                                    <?php else: ?>
                                                        - Free
                                                    <?php endif; ?>
                                                </p>
                                            </div>
                                            <button class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-lg hover:opacity-90 text-sm font-medium transition-opacity">
                                                <?php echo $planData['price'] > 0 ? 'Upgrade' : 'Downgrade'; ?>
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Available Models -->
                    <div class="chart-container mt-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-light">Available AI Models</h3>
                            <span class="badge badge-success"><?php echo count($modelMapping); ?> Models</span>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <?php foreach ($modelMapping as $displayName => $actualName): ?>
                                <div class="border border-gray-600 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-900 hover:bg-opacity-20 transition-all">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h4 class="font-medium text-light text-sm"><?php echo htmlspecialchars($displayName); ?></h4>
                                            <p class="text-xs text-muted mt-1 font-mono"><?php echo htmlspecialchars($actualName); ?></p>
                                        </div>
                                        <div class="w-2 h-2 bg-green-400 rounded-full ml-2 mt-1"></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

<?php require_once 'includes/footer.php'; ?>
