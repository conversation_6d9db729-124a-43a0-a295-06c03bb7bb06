<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

requireLogin();

$user = getUserById($_SESSION['user_id']);
$stats = getUserStats($_SESSION['user_id']);
$planLimits = getPlanLimits();
$modelMapping = getModelMapping();

$welcome = isset($_GET['welcome']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - AI API Provider</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-bold text-gray-800">AI API Provider</h1>
                    </div>
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="dashboard.php" class="text-blue-600 px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                        <a href="docs.php" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">API Docs</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">Welcome, <?php echo htmlspecialchars($user['email']); ?></span>
                    <a href="logout.php" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 px-4">
        <?php if ($welcome): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <strong>Welcome!</strong> Your account has been created successfully. Your API key is ready to use.
            </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p class="text-gray-600">Manage your API usage and settings</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i class="fas fa-key text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Current Plan</p>
                        <p class="text-lg font-semibold text-gray-900"><?php echo ucfirst($user['plan']); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <i class="fas fa-chart-line text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Messages Used</p>
                        <p class="text-lg font-semibold text-gray-900">
                            <?php echo formatNumber($user['requests_used']); ?> / <?php echo formatNumber($user['requests_limit']); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 rounded-lg">
                        <i class="fas fa-percentage text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Usage</p>
                        <p class="text-lg font-semibold text-gray-900">
                            <?php echo calculatePercentage($user['requests_used'], $user['requests_limit']); ?>%
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <i class="fas fa-clock text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Remaining</p>
                        <p class="text-lg font-semibold text-gray-900">
                            <?php echo formatNumber(getRemainingRequests($user)); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Progress Bar -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Monthly Message Usage</h3>
            <div class="w-full bg-gray-200 rounded-full h-4">
                <div class="bg-blue-600 h-4 rounded-full" style="width: <?php echo calculatePercentage($user['requests_used'], $user['requests_limit']); ?>%"></div>
            </div>
            <div class="flex justify-between text-sm text-gray-600 mt-2">
                <span><?php echo formatNumber($user['requests_used']); ?> used</span>
                <span><?php echo formatNumber($user['requests_limit']); ?> total</span>
            </div>

            <?php if (needsPlanUpgrade($user)): ?>
                <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p class="text-red-800">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        You've reached your monthly message limit. <a href="#upgrade" class="font-semibold underline">Upgrade your plan</a> to continue using the API.
                    </p>
                </div>
            <?php endif; ?>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- API Key Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">API Key</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Your API Key</label>
                        <div class="flex">
                            <input type="password" id="apiKey" value="<?php echo htmlspecialchars($user['api_key']); ?>"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50" readonly>
                            <button onclick="toggleApiKey()"
                                    class="px-4 py-2 bg-gray-500 text-white rounded-r-md hover:bg-gray-600">
                                <i class="fas fa-eye" id="eyeIcon"></i>
                            </button>
                        </div>
                        <p class="text-sm text-gray-500 mt-2">Keep your API key secure and don't share it publicly.</p>
                    </div>

                    <div>
                        <h4 class="font-medium text-gray-900 mb-2">Quick Start</h4>
                        <div class="bg-gray-100 p-3 rounded text-sm">
                            <code>curl -X POST http://<?php echo $_SERVER['HTTP_HOST']; ?>/api/v1/chat/completions \<br>
  -H "Authorization: Bearer <?php echo substr($user['api_key'], 0, 20); ?>..." \<br>
  -H "Content-Type: application/json" \<br>
  -d '{"model": "claude-3.5-sonnet", "messages": [{"role": "user", "content": "Hello!"}]}'</code>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Plan Upgrade Section -->
            <div class="bg-white rounded-lg shadow p-6" id="upgrade">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Upgrade Plan</h3>
                <div class="space-y-4">
                    <?php foreach ($planLimits as $planName => $planData): ?>
                        <?php if ($planName === $user['plan']) continue; ?>
                        <div class="border rounded-lg p-4 <?php echo $planName === 'pro' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'; ?>">
                            <div class="flex justify-between items-center">
                                <div>
                                    <h4 class="font-semibold text-gray-900"><?php echo ucfirst($planName); ?></h4>
                                    <p class="text-sm text-gray-600">
                                        <?php echo formatNumber($planData['limit']); ?> user messages/month
                                        <?php if ($planData['price'] > 0): ?>
                                            - $<?php echo $planData['price']; ?>/month
                                        <?php else: ?>
                                            - Free
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 text-sm">
                                    <?php echo $planData['price'] > 0 ? 'Upgrade' : 'Downgrade'; ?>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Available Models -->
        <div class="bg-white rounded-lg shadow p-6 mt-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Available Models</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php foreach ($modelMapping as $displayName => $actualName): ?>
                    <div class="border rounded-lg p-3">
                        <h4 class="font-medium text-gray-900"><?php echo htmlspecialchars($displayName); ?></h4>
                        <p class="text-sm text-gray-600"><?php echo htmlspecialchars($actualName); ?></p>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <script>
        function toggleApiKey() {
            const apiKeyInput = document.getElementById('apiKey');
            const eyeIcon = document.getElementById('eyeIcon');

            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
                eyeIcon.className = 'fas fa-eye-slash';
            } else {
                apiKeyInput.type = 'password';
                eyeIcon.className = 'fas fa-eye';
            }
        }
    </script>
</body>
</html>
