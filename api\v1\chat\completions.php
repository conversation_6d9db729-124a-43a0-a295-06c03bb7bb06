<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(getApiErrorResponse('Method not allowed', 405));
    exit;
}

require_once __DIR__ . '/../../../config/database.php';
require_once __DIR__ . '/../../../includes/functions.php';
require_once __DIR__ . '/../../../config/security.php';

// Initialize security (without session for API)
setSecurityHeaders();

// Get authorization header
$headers = getallheaders();
$authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';

if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    http_response_code(401);
    echo json_encode(getApiErrorResponse('Missing or invalid authorization header', 401));
    exit;
}

$apiKey = $matches[1];

// Validate API key format
if (!validateApiKey($apiKey)) {
    http_response_code(401);
    echo json_encode(getApiErrorResponse('Invalid API key format', 401));
    logSecurityEvent('invalid_api_key_format', ['api_key' => substr($apiKey, 0, 10) . '...']);
    exit;
}

// Check for compromised API key
if (checkApiKeyCompromise($apiKey)) {
    http_response_code(401);
    echo json_encode(getApiErrorResponse('API key appears to be compromised', 401));
    logSecurityEvent('compromised_api_key', ['api_key' => substr($apiKey, 0, 10) . '...']);
    exit;
}

// Get user by API key
$user = getUserByApiKey($apiKey);
if (!$user) {
    http_response_code(401);
    echo json_encode(getApiErrorResponse('Invalid API key', 401));
    exit;
}

// Check if user can make request
if (!canMakeRequest($user['id'])) {
    http_response_code(429);
    echo json_encode(getApiErrorResponse('Rate limit exceeded. Upgrade your plan for more requests.', 429));
    exit;
}

// Get request body
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    http_response_code(400);
    echo json_encode(getApiErrorResponse('Invalid JSON in request body', 400));
    exit;
}

// Validate required fields
if (!isset($data['model'])) {
    http_response_code(400);
    echo json_encode(getApiErrorResponse('Missing required field: model', 400));
    exit;
}

if (!isset($data['messages']) || !is_array($data['messages'])) {
    http_response_code(400);
    echo json_encode(getApiErrorResponse('Missing or invalid required field: messages', 400));
    exit;
}

// Get model mapping
$modelMapping = getModelMapping();
$requestedModel = $data['model'];

if (!isset($modelMapping[$requestedModel])) {
    http_response_code(400);
    echo json_encode(getApiErrorResponse('Unsupported model: ' . $requestedModel, 400));
    exit;
}

$actualModel = $modelMapping[$requestedModel];

// Prepare request for upstream API
$upstreamData = $data;
$upstreamData['model'] = $actualModel;

// Proxy request to upstream API
$startTime = microtime(true);

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => 'https://oi-vscode-server-5.onrender.com/v1/chat/completions',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($upstreamData),
    CURLOPT_HTTPHEADER => [
        'Accept: application/json',
        'Content-Type: application/json',
        'User-Agent: Pr/JS 4.73.1',
        'X-Stainless-Lang: js',
        'X-Stainless-Package-Version: 4.73.1',
        'X-Stainless-OS: Windows',
        'X-Stainless-Arch: x64',
        'X-Stainless-Runtime: node',
        'X-Stainless-Runtime-Version: v20.18.2',
        'Authorization: Bearer xxx',
        'CustomerID: cus_SBLPR2vIB7Tp4z',
        'X-Stainless-Retry-Count: 0',
        'Connection: close',
        'Accept-Encoding: gzip, deflate'
    ],
    CURLOPT_TIMEOUT => 60,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_ENCODING => 'gzip, deflate'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$responseTime = microtime(true) - $startTime;

if (curl_error($ch)) {
    curl_close($ch);
    http_response_code(502);
    echo json_encode(getApiErrorResponse('Upstream API error: ' . curl_error($ch), 502));
    exit;
}

curl_close($ch);

// Parse response
$responseData = json_decode($response, true);

// Calculate tokens used (estimate if not provided)
$tokensUsed = 0;
if (isset($responseData['usage']['total_tokens'])) {
    $tokensUsed = $responseData['usage']['total_tokens'];
} else {
    // Estimate tokens based on message length
    $totalChars = 0;
    foreach ($data['messages'] as $message) {
        $totalChars += strlen($message['content'] ?? '');
    }
    if (isset($responseData['choices'][0]['message']['content'])) {
        $totalChars += strlen($responseData['choices'][0]['message']['content']);
    }
    $tokensUsed = ceil($totalChars / 4); // Rough estimate: 4 chars per token
}

// Log the request
logApiRequest($user['id'], $requestedModel, $actualModel, $tokensUsed, $responseTime, $httpCode);

// Increment user request count
incrementRequestCount($user['id']);

// Return response with original model name
if ($responseData && isset($responseData['model'])) {
    $responseData['model'] = $requestedModel;
}

http_response_code($httpCode);
echo $response;
?>
