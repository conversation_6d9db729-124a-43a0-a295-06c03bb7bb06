<?php
require_once __DIR__ . '/../config/database.php';

// Generate API key
function generateApiKey() {
    return 'sk-' . bin2hex(random_bytes(24));
}

// Hash password
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Verify password
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Get user by ID
function getUserById($id) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Get user by email
function getUserByEmail($email) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Get user by API key
function getUserByApiKey($apiKey) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("SELECT * FROM users WHERE api_key = ?");
    $stmt->execute([$apiKey]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Create new user
function createUser($email, $password, $plan = 'basic') {
    $pdo = getConnection();
    $hashedPassword = hashPassword($password);
    $apiKey = generateApiKey();
    $planLimits = getPlanLimits();
    $requestsLimit = $planLimits[$plan]['limit'];

    $stmt = $pdo->prepare("INSERT INTO users (email, password, plan, api_key, requests_limit) VALUES (?, ?, ?, ?, ?)");

    try {
        $stmt->execute([$email, $hashedPassword, $plan, $apiKey, $requestsLimit]);
        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return false;
    }
}

// Update user plan
function updateUserPlan($userId, $plan) {
    $pdo = getConnection();
    $planLimits = getPlanLimits();
    $requestsLimit = $planLimits[$plan]['limit'];

    $stmt = $pdo->prepare("UPDATE users SET plan = ?, requests_limit = ?, requests_used = 0 WHERE id = ?");
    return $stmt->execute([$plan, $requestsLimit, $userId]);
}

// Check if user can make request
function canMakeRequest($userId) {
    $user = getUserById($userId);
    if (!$user) return false;

    return $user['requests_used'] < $user['requests_limit'];
}

// Increment user request count
function incrementRequestCount($userId) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("UPDATE users SET requests_used = requests_used + 1 WHERE id = ?");
    return $stmt->execute([$userId]);
}

// Log API request
function logApiRequest($userId, $modelRequested, $modelActual, $tokensUsed = 0, $responseTime = 0, $statusCode = 200) {
    $pdo = getConnection();
    $stmt = $pdo->prepare("INSERT INTO api_requests (user_id, model_requested, model_actual, tokens_used, response_time, status_code) VALUES (?, ?, ?, ?, ?, ?)");
    return $stmt->execute([$userId, $modelRequested, $modelActual, $tokensUsed, $responseTime, $statusCode]);
}

// Get user statistics
function getUserStats($userId) {
    $pdo = getConnection();

    // Get total requests this month
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_requests FROM api_requests WHERE user_id = ? AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
    $stmt->execute([$userId]);
    $monthlyRequests = $stmt->fetch(PDO::FETCH_ASSOC)['total_requests'];

    // Get total tokens used this month
    $stmt = $pdo->prepare("SELECT SUM(tokens_used) as total_tokens FROM api_requests WHERE user_id = ? AND MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())");
    $stmt->execute([$userId]);
    $monthlyTokens = $stmt->fetch(PDO::FETCH_ASSOC)['total_tokens'] ?? 0;

    // Get most used model
    $stmt = $pdo->prepare("SELECT model_requested, COUNT(*) as count FROM api_requests WHERE user_id = ? GROUP BY model_requested ORDER BY count DESC LIMIT 1");
    $stmt->execute([$userId]);
    $mostUsedModel = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        'monthly_requests' => $monthlyRequests,
        'monthly_tokens' => $monthlyTokens,
        'most_used_model' => $mostUsedModel ? $mostUsedModel['model_requested'] : 'None'
    ];
}

// Validate email format
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)));
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// Redirect if not logged in
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

// Get plan display name
function getPlanDisplayName($plan) {
    $names = [
        'basic' => 'Basic (Free)',
        'pro' => 'Pro ($20/month)',
        'developer' => 'Developer ($50/month)',
        'enterprise' => 'Enterprise ($150/month)'
    ];
    return $names[$plan] ?? $plan;
}

// Format number with commas
function formatNumber($number) {
    return number_format($number);
}

// Calculate percentage
function calculatePercentage($used, $total) {
    if ($total == 0) return 0;
    return round(($used / $total) * 100, 1);
}

// Get remaining user messages
function getRemainingRequests($user) {
    return max(0, $user['requests_limit'] - $user['requests_used']);
}

// Check if plan upgrade is needed
function needsPlanUpgrade($user) {
    return $user['requests_used'] >= $user['requests_limit'];
}

// Generate secure token
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// Validate API key format
function isValidApiKey($apiKey) {
    return preg_match('/^sk-[a-f0-9]{48}$/', $apiKey);
}

// Get error message for API response
function getApiErrorResponse($message, $code = 400) {
    return [
        'error' => [
            'message' => $message,
            'type' => 'invalid_request_error',
            'code' => $code
        ]
    ];
}

// Get success response for API
function getApiSuccessResponse($data) {
    return array_merge([
        'object' => 'chat.completion',
        'created' => time(),
        'model' => $data['model'] ?? 'unknown'
    ], $data);
}
?>
