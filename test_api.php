<?php
// Test script for the API
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h1>API Test Script</h1>";

// Test 1: Database connection
echo "<h2>1. Testing Database Connection</h2>";
try {
    $pdo = getConnection();
    echo "✅ Database connection successful<br>";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Test 2: Create test user
echo "<h2>2. Testing User Creation</h2>";
$testEmail = '<EMAIL>';
$testPassword = 'testpassword123';

// Check if test user already exists
$existingUser = getUserByEmail($testEmail);
if ($existingUser) {
    echo "ℹ️ Test user already exists<br>";
    $testUser = $existingUser;
} else {
    $userId = createUser($testEmail, $testPassword, 'basic');
    if ($userId) {
        echo "✅ Test user created successfully<br>";
        $testUser = getUserById($userId);
    } else {
        echo "❌ Failed to create test user<br>";
        exit;
    }
}

echo "Test user API key: " . $testUser['api_key'] . "<br>";

// Test 3: API Key validation
echo "<h2>3. Testing API Key Validation</h2>";
if (isValidApiKey($testUser['api_key'])) {
    echo "✅ API key format is valid<br>";
} else {
    echo "❌ API key format is invalid<br>";
}

$retrievedUser = getUserByApiKey($testUser['api_key']);
if ($retrievedUser && $retrievedUser['id'] == $testUser['id']) {
    echo "✅ API key lookup successful<br>";
} else {
    echo "❌ API key lookup failed<br>";
}

// Test 4: Model mapping
echo "<h2>4. Testing Model Mapping</h2>";
$modelMapping = getModelMapping();
echo "Available models: " . count($modelMapping) . "<br>";
foreach ($modelMapping as $display => $actual) {
    echo "- $display → $actual<br>";
}

// Test 5: Rate limiting
echo "<h2>5. Testing Rate Limiting</h2>";
if (canMakeRequest($testUser['id'])) {
    echo "✅ User can make requests<br>";
    echo "Requests used: " . $testUser['requests_used'] . "/" . $testUser['requests_limit'] . "<br>";
} else {
    echo "❌ User has reached rate limit<br>";
}

// Test 6: API endpoint test
echo "<h2>6. Testing API Endpoint</h2>";
$apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/api/v1/chat/completions';
$testData = [
    'model' => 'claude-3.5-sonnet',
    'messages' => [
        [
            'role' => 'user',
            'content' => 'Hello, this is a test message.'
        ]
    ],
    'max_tokens' => 50
];

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $apiUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($testData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $testUser['api_key']
    ],
    CURLOPT_TIMEOUT => 30
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Status Code: $httpCode<br>";
if ($error) {
    echo "❌ cURL Error: $error<br>";
} else {
    echo "✅ API request completed<br>";
    echo "Response: <pre>" . htmlspecialchars($response) . "</pre>";
}

// Test 7: Statistics
echo "<h2>7. Testing Statistics</h2>";
$stats = getUserStats($testUser['id']);
echo "Monthly requests: " . $stats['monthly_requests'] . "<br>";
echo "Monthly tokens: " . $stats['monthly_tokens'] . "<br>";
echo "Most used model: " . $stats['most_used_model'] . "<br>";

echo "<h2>Test Complete</h2>";
echo "<p><a href='index.php'>← Back to Home</a></p>";
?>
