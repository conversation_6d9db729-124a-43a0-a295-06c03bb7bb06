<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'config/security.php';

initSecurityBeforeSession();
session_start();
initSecurityAfterSession();

$user = null;
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
}

$modelMapping = getModelMapping();

// Set page variables for header
$pageTitle = 'API Documentation';
$pageHeader = true;
$pageDescription = 'Complete guide to using our AI API';

// Include header
require_once 'includes/header.php';
?>

<!-- Additional CSS for docs -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism-dark.min.css" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>

<style>
    pre[class*="language-"] {
        background: #1a1d29 !important;
        border: 1px solid #2d3748;
        border-radius: 8px;
    }
    code[class*="language-"] {
        color: #e2e8f0 !important;
    }
    .docs-section {
        background: #252a3d;
        border: 1px solid #2d3748;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 32px;
    }
    .docs-table {
        background: #1a1d29;
        border: 1px solid #2d3748;
        border-radius: 8px;
        overflow: hidden;
    }
    .docs-table th {
        background: #252a3d;
        color: #e2e8f0;
        border-bottom: 1px solid #2d3748;
    }
    .docs-table td {
        color: #a0aec0;
        border-bottom: 1px solid #2d3748;
    }
</style>

        <!-- Quick Start -->
        <div class="docs-section">
            <h2 class="text-2xl font-bold text-light mb-4">Quick Start</h2>

            <div class="mb-6">
                <h3 class="text-lg font-semibold text-light mb-2">1. Get Your API Key</h3>
                <p class="text-muted mb-4">
                    <?php if ($user): ?>
                        Your API key is available in your <a href="dashboard.php" class="text-blue-400 hover:underline">dashboard</a>.
                    <?php else: ?>
                        <a href="register.php" class="text-blue-400 hover:underline">Sign up</a> to get your free API key with 100 user messages per month.
                    <?php endif; ?>
                </p>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold text-light mb-2">2. Make Your First Request</h3>
                <pre class="bg-gray-800 p-4 rounded-lg overflow-x-auto border border-gray-600"><code class="language-bash">curl -X POST http://<?php echo $_SERVER['HTTP_HOST']; ?>/api/v1/chat/completions \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3.5-sonnet",
    "messages": [
      {
        "role": "user",
        "content": "Hello! How are you?"
      }
    ]
  }'</code></pre>
            </div>
        </div>

        <!-- Authentication -->
        <div class="docs-section">
            <h2 class="text-2xl font-bold text-light mb-4">Authentication</h2>
            <p class="text-muted mb-4">
                All API requests must include your API key in the Authorization header:
            </p>
            <pre class="bg-gray-800 p-4 rounded-lg border border-gray-600"><code class="language-bash">Authorization: Bearer YOUR_API_KEY</code></pre>
        </div>

        <!-- Endpoints -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Endpoints</h2>

            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Chat Completions</h3>
                <div class="bg-gray-50 p-4 rounded-lg mb-4">
                    <code class="text-sm">POST /api/v1/chat/completions</code>
                </div>

                <h4 class="font-semibold text-gray-900 mb-2">Request Body</h4>
                <pre class="bg-gray-100 p-4 rounded-lg mb-4 overflow-x-auto"><code class="language-json">{
  "model": "claude-3.5-sonnet",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello!"
    }
  ],
  "max_tokens": 1000,
  "temperature": 0.7,
  "stream": false
}</code></pre>

                <h4 class="font-semibold text-gray-900 mb-2">Parameters</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">model</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">string</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Yes</td>
                                <td class="px-6 py-4 text-sm text-gray-500">The model to use for completion</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">messages</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">array</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Yes</td>
                                <td class="px-6 py-4 text-sm text-gray-500">Array of message objects</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">max_tokens</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">integer</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">No</td>
                                <td class="px-6 py-4 text-sm text-gray-500">Maximum tokens to generate</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">temperature</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">number</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">No</td>
                                <td class="px-6 py-4 text-sm text-gray-500">Sampling temperature (0-2)</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Available Models -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Available Models</h2>
            <p class="text-gray-600 mb-4">
                We support the following AI models. Use the display name in your requests:
            </p>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Display Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Provider</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($modelMapping as $displayName => $actualName): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    <code><?php echo htmlspecialchars($displayName); ?></code>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php
                                    if (strpos($displayName, 'claude') !== false) echo 'Anthropic';
                                    elseif (strpos($displayName, 'gpt') !== false || strpos($displayName, 'o1') !== false || strpos($displayName, 'o3') !== false || strpos($displayName, 'o4') !== false) echo 'OpenAI';
                                    elseif (strpos($displayName, 'qwen') !== false) echo 'Alibaba';
                                    elseif (strpos($displayName, 'deepseek') !== false) echo 'DeepSeek';
                                    elseif (strpos($displayName, 'llama') !== false) echo 'Meta';
                                    elseif (strpos($displayName, 'grok') !== false) echo 'xAI';
                                    elseif (strpos($displayName, 'gemini') !== false) echo 'Google';
                                    else echo 'Various';
                                    ?>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">
                                    <?php
                                    if (strpos($displayName, 'claude-4') !== false) echo 'Latest Claude model with enhanced capabilities';
                                    elseif (strpos($displayName, 'claude-3.7') !== false) echo 'Advanced Claude model';
                                    elseif (strpos($displayName, 'claude-3.5') !== false) echo 'Balanced Claude model';
                                    elseif (strpos($displayName, 'o4') !== false) echo 'Latest OpenAI reasoning model';
                                    elseif (strpos($displayName, 'o3') !== false) echo 'OpenAI reasoning model';
                                    elseif (strpos($displayName, 'o1') !== false) echo 'OpenAI reasoning model';
                                    elseif (strpos($displayName, 'gpt-4o') !== false) echo 'OpenAI multimodal model';
                                    else echo 'High-performance AI model';
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Rate Limits -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Rate Limits</h2>
            <p class="text-gray-600 mb-4">
                Rate limits are based on your subscription plan:
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="border rounded-lg p-4">
                    <h3 class="font-semibold text-gray-900">Basic (Free)</h3>
                    <p class="text-2xl font-bold text-blue-600">100</p>
                    <p class="text-sm text-gray-600">user messages/month</p>
                </div>
                <div class="border rounded-lg p-4">
                    <h3 class="font-semibold text-gray-900">Pro</h3>
                    <p class="text-2xl font-bold text-blue-600">500</p>
                    <p class="text-sm text-gray-600">user messages/month</p>
                </div>
                <div class="border rounded-lg p-4">
                    <h3 class="font-semibold text-gray-900">Developer</h3>
                    <p class="text-2xl font-bold text-blue-600">2,500</p>
                    <p class="text-sm text-gray-600">user messages/month</p>
                </div>
                <div class="border rounded-lg p-4">
                    <h3 class="font-semibold text-gray-900">Enterprise</h3>
                    <p class="text-2xl font-bold text-blue-600">10,000</p>
                    <p class="text-sm text-gray-600">user messages/month</p>
                </div>
            </div>
        </div>

        <!-- Error Codes -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Error Codes</h2>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">400</td>
                            <td class="px-6 py-4 text-sm text-gray-500">Bad Request - Invalid request format</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">401</td>
                            <td class="px-6 py-4 text-sm text-gray-500">Unauthorized - Invalid API key</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">429</td>
                            <td class="px-6 py-4 text-sm text-gray-500">Rate Limit Exceeded - Upgrade your plan</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">500</td>
                            <td class="px-6 py-4 text-sm text-gray-500">Internal Server Error</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">502</td>
                            <td class="px-6 py-4 text-sm text-gray-500">Bad Gateway - Upstream API error</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

<?php require_once 'includes/footer.php'; ?>
