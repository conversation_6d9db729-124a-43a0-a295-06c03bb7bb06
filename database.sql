-- Database setup for OpenAI API Provider
-- Run this SQL script in phpMyAdmin or MySQL command line if automatic setup fails

CREATE DATABASE IF NOT EXISTS openai_api_provider;
USE openai_api_provider;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    plan ENUM('basic', 'pro', 'developer', 'enterprise') DEFAULT 'basic',
    api_key VARCHAR(255) UNIQUE NOT NULL,
    requests_used INT DEFAULT 0,
    requests_limit INT DEFAULT 100,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_api_key (api_key),
    INDEX idx_plan (plan)
);

-- API requests logging table
CREATE TABLE IF NOT EXISTS api_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    model_requested VARCHAR(100) NOT NULL,
    model_actual VARCHAR(100) NOT NULL,
    tokens_used INT DEFAULT 0,
    response_time FLOAT DEFAULT 0,
    status_code INT DEFAULT 200,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_model_requested (model_requested)
);

-- Payments table for future payment integration
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    plan VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Insert sample data for testing (optional)
-- INSERT INTO users (email, password, plan, api_key, requests_limit) VALUES 
-- ('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'enterprise', 'sk-test1234567890abcdef1234567890abcdef123456', 10000),
-- ('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'basic', 'sk-test0987654321fedcba0987654321fedcba098765', 100);

-- Create indexes for better performance
CREATE INDEX idx_users_plan_requests ON users(plan, requests_used);
CREATE INDEX idx_api_requests_user_date ON api_requests(user_id, created_at);
CREATE INDEX idx_api_requests_model_date ON api_requests(model_requested, created_at);

-- Views for analytics (optional)
CREATE OR REPLACE VIEW user_monthly_stats AS
SELECT 
    u.id,
    u.email,
    u.plan,
    u.requests_used,
    u.requests_limit,
    COALESCE(monthly.requests_this_month, 0) as requests_this_month,
    COALESCE(monthly.tokens_this_month, 0) as tokens_this_month,
    COALESCE(monthly.avg_response_time, 0) as avg_response_time
FROM users u
LEFT JOIN (
    SELECT 
        user_id,
        COUNT(*) as requests_this_month,
        SUM(tokens_used) as tokens_this_month,
        AVG(response_time) as avg_response_time
    FROM api_requests 
    WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) 
    AND YEAR(created_at) = YEAR(CURRENT_DATE())
    GROUP BY user_id
) monthly ON u.id = monthly.user_id;

-- Model usage statistics view
CREATE OR REPLACE VIEW model_usage_stats AS
SELECT 
    model_requested,
    COUNT(*) as total_requests,
    SUM(tokens_used) as total_tokens,
    AVG(response_time) as avg_response_time,
    COUNT(DISTINCT user_id) as unique_users,
    DATE(created_at) as request_date
FROM api_requests 
GROUP BY model_requested, DATE(created_at)
ORDER BY request_date DESC, total_requests DESC;

-- Daily usage statistics view
CREATE OR REPLACE VIEW daily_usage_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_requests,
    SUM(tokens_used) as total_tokens,
    COUNT(DISTINCT user_id) as active_users,
    AVG(response_time) as avg_response_time
FROM api_requests 
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Stored procedure to reset monthly usage (run monthly via cron)
DELIMITER //
CREATE PROCEDURE ResetMonthlyUsage()
BEGIN
    UPDATE users SET requests_used = 0;
    INSERT INTO api_requests (user_id, model_requested, model_actual, tokens_used, response_time, status_code) 
    SELECT 0, 'SYSTEM', 'MONTHLY_RESET', 0, 0, 200 FROM DUAL;
END //
DELIMITER ;

-- Stored procedure to upgrade user plan
DELIMITER //
CREATE PROCEDURE UpgradeUserPlan(IN user_id INT, IN new_plan VARCHAR(50))
BEGIN
    DECLARE new_limit INT DEFAULT 100;
    
    CASE new_plan
        WHEN 'basic' THEN SET new_limit = 100;
        WHEN 'pro' THEN SET new_limit = 500;
        WHEN 'developer' THEN SET new_limit = 2500;
        WHEN 'enterprise' THEN SET new_limit = 10000;
    END CASE;
    
    UPDATE users 
    SET plan = new_plan, 
        requests_limit = new_limit,
        requests_used = 0
    WHERE id = user_id;
END //
DELIMITER ;

-- Function to check if user can make request
DELIMITER //
CREATE FUNCTION CanUserMakeRequest(user_id INT) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE current_usage INT DEFAULT 0;
    DECLARE usage_limit INT DEFAULT 0;
    
    SELECT requests_used, requests_limit 
    INTO current_usage, usage_limit
    FROM users 
    WHERE id = user_id;
    
    RETURN current_usage < usage_limit;
END //
DELIMITER ;

-- Trigger to automatically update user stats
DELIMITER //
CREATE TRIGGER update_user_stats_after_request
AFTER INSERT ON api_requests
FOR EACH ROW
BEGIN
    UPDATE users 
    SET requests_used = requests_used + 1 
    WHERE id = NEW.user_id;
END //
DELIMITER ;

-- Show table information
SHOW TABLES;
DESCRIBE users;
DESCRIBE api_requests;
DESCRIBE payments;
