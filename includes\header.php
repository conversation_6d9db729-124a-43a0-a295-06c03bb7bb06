<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle : 'AI API Provider'; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1d29;
            color: #e2e8f0;
        }
        .dark-bg {
            background: #1a1d29;
        }
        .sidebar {
            background: #252a3d;
            border-right: 1px solid #2d3748;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
        }
        .main-content {
            background: #1a1d29;
            min-height: 100vh;
        }
        .card {
            background: #252a3d;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            border: 1px solid #2d3748;
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            transform: translateY(-2px);
        }
        .stat-card {
            background: #252a3d;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #2d3748;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .nav-link {
            transition: all 0.3s ease;
            border-radius: 8px;
            color: #a0aec0;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }
        .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #e2e8f0;
            transform: translateY(-1px);
        }
        .nav-link.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .nav-link.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
            pointer-events: none;
        }
        .header-dark {
            background: rgba(37, 42, 61, 0.95);
            border-bottom: 1px solid #2d3748;
            backdrop-filter: blur(10px);
        }
        .dropdown-menu {
            backdrop-filter: blur(10px);
            background: rgba(31, 41, 55, 0.95);
        }
        .search-input {
            backdrop-filter: blur(10px);
            background: rgba(55, 65, 81, 0.8);
        }
        .search-input:focus {
            background: rgba(55, 65, 81, 1);
        }
        .status-indicator {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .notification-badge {
            animation: bounce 1s infinite;
        }
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0,-3px,0); }
            70% { transform: translate3d(0,-2px,0); }
            90% { transform: translate3d(0,-1px,0); }
        }
        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 6px;
            height: 8px;
        }
        .enhanced-progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            background-size: 200% 100%;
            animation: gradient-shift 3s ease-in-out infinite;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            position: relative;
        }
        .enhanced-progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
            border-radius: inherit;
        }
        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        @keyframes shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        .animate-shine {
            animation: shine 2s ease-in-out infinite;
        }
        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .badge-success { background: rgba(16, 185, 129, 0.2); color: #10b981; border: 1px solid #10b981; }
        .badge-warning { background: rgba(245, 158, 11, 0.2); color: #f59e0b; border: 1px solid #f59e0b; }
        .badge-info { background: rgba(59, 130, 246, 0.2); color: #3b82f6; border: 1px solid #3b82f6; }
        .badge-purple { background: rgba(139, 92, 246, 0.2); color: #8b5cf6; border: 1px solid #8b5cf6; }
        .table-row {
            transition: all 0.2s ease;
        }
        .table-row:hover {
            background: rgba(102, 126, 234, 0.1);
        }
        .chart-container {
            background: #252a3d;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #2d3748;
        }
        .mini-chart {
            width: 60px;
            height: 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        .mini-chart::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%);
        }
        .header-dark {
            background: #252a3d;
            border-bottom: 1px solid #2d3748;
        }
        .text-light { color: #e2e8f0; }
        .text-muted { color: #a0aec0; }
        .text-success { color: #10b981; }
        .text-warning { color: #f59e0b; }
        .text-info { color: #3b82f6; }
        .bg-dark-card { background: #252a3d; }
        .border-dark { border-color: #2d3748; }
        .dark-bg { background-color: #1a1d29; }

        /* Enhanced Responsive Design System */
        .container-responsive {
            width: 100%;
            padding-left: 1rem;
            padding-right: 1rem;
            margin-left: auto;
            margin-right: auto;
        }

        @media (min-width: 640px) {
            .container-responsive {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
                max-width: 640px;
            }
        }

        @media (min-width: 768px) {
            .container-responsive {
                padding-left: 2rem;
                padding-right: 2rem;
                max-width: 768px;
            }
        }

        @media (min-width: 1024px) {
            .container-responsive { max-width: 1024px; }
        }

        @media (min-width: 1280px) {
            .container-responsive { max-width: 1280px; }
        }

        @media (min-width: 1536px) {
            .container-responsive { max-width: 1536px; }
        }

        /* Flexible Grid System */
        .grid-responsive {
            display: grid;
            gap: 1rem;
            grid-template-columns: 1fr;
        }

        @media (min-width: 640px) {
            .grid-responsive-sm-2 { grid-template-columns: repeat(2, 1fr); }
            .grid-responsive-sm-3 { grid-template-columns: repeat(3, 1fr); }
        }

        @media (min-width: 768px) {
            .grid-responsive-md-2 { grid-template-columns: repeat(2, 1fr); }
            .grid-responsive-md-3 { grid-template-columns: repeat(3, 1fr); }
            .grid-responsive-md-4 { grid-template-columns: repeat(4, 1fr); }
        }

        @media (min-width: 1024px) {
            .grid-responsive-lg-2 { grid-template-columns: repeat(2, 1fr); }
            .grid-responsive-lg-3 { grid-template-columns: repeat(3, 1fr); }
            .grid-responsive-lg-4 { grid-template-columns: repeat(4, 1fr); }
            .grid-responsive-lg-5 { grid-template-columns: repeat(5, 1fr); }
        }

        /* Responsive Typography */
        .text-responsive-xs { font-size: clamp(0.75rem, 2vw, 0.875rem); }
        .text-responsive-sm { font-size: clamp(0.875rem, 2.5vw, 1rem); }
        .text-responsive-base { font-size: clamp(1rem, 3vw, 1.125rem); }
        .text-responsive-lg { font-size: clamp(1.125rem, 3.5vw, 1.25rem); }
        .text-responsive-xl { font-size: clamp(1.25rem, 4vw, 1.5rem); }
        .text-responsive-2xl { font-size: clamp(1.5rem, 5vw, 2rem); }
        .text-responsive-3xl { font-size: clamp(2rem, 6vw, 3rem); }
        .text-responsive-4xl { font-size: clamp(2.5rem, 7vw, 4rem); }

        /* Flexible Spacing */
        .space-responsive { margin: clamp(1rem, 4vw, 2rem); }
        .space-responsive-sm { margin: clamp(0.5rem, 2vw, 1rem); }
        .space-responsive-lg { margin: clamp(2rem, 6vw, 4rem); }
        .space-responsive-xl { margin: clamp(3rem, 8vw, 6rem); }

        .padding-responsive { padding: clamp(1rem, 4vw, 2rem); }
        .padding-responsive-sm { padding: clamp(0.5rem, 2vw, 1rem); }
        .padding-responsive-lg { padding: clamp(2rem, 6vw, 4rem); }

        /* Responsive Cards */
        .card-responsive {
            padding: clamp(1rem, 4vw, 2rem);
            border-radius: clamp(0.5rem, 2vw, 1rem);
            margin-bottom: clamp(1rem, 3vw, 1.5rem);
        }

        /* Mobile-first Navigation Improvements */
        @media (max-width: 1024px) {
            .nav-link {
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
            }
            .header-dark .max-w-7xl {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }

        @media (max-width: 768px) {
            .nav-link {
                padding: 1rem;
                font-size: 1rem;
                border-bottom: 1px solid #2d3748;
            }
            .chart-container {
                padding: 1rem;
                margin-bottom: 1rem;
            }
            .pricing-card, .model-card {
                padding: 1.5rem;
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 640px) {
            .nav-link {
                padding: 1.25rem 1rem;
                font-size: 1rem;
            }
            .text-responsive-hero {
                font-size: clamp(2rem, 8vw, 3rem);
                line-height: 1.2;
            }
            .button-responsive {
                padding: 0.875rem 1.5rem;
                font-size: 0.875rem;
                width: 100%;
                text-align: center;
            }
        }

        /* Flexible Button System */
        .btn-responsive {
            padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 4vw, 1.5rem);
            font-size: clamp(0.875rem, 2.5vw, 1rem);
            border-radius: clamp(0.375rem, 1vw, 0.5rem);
            transition: all 0.3s ease;
        }

        @media (max-width: 640px) {
            .btn-responsive {
                width: 100%;
                text-align: center;
                margin-bottom: 0.5rem;
            }
        }

        /* Responsive Tables */
        @media (max-width: 768px) {
            .table-responsive {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }
            .table-responsive table {
                min-width: 600px;
            }
        }

        /* Flexible Form Elements */
        .form-responsive input,
        .form-responsive select,
        .form-responsive textarea {
            width: 100%;
            padding: clamp(0.5rem, 2vw, 0.75rem);
            font-size: clamp(0.875rem, 2.5vw, 1rem);
            border-radius: clamp(0.375rem, 1vw, 0.5rem);
        }

        /* Responsive Utilities */
        .hide-mobile { display: block; }
        .show-mobile { display: none; }

        @media (max-width: 768px) {
            .hide-mobile { display: none; }
            .show-mobile { display: block; }
        }

        .hide-tablet { display: block; }
        .show-tablet { display: none; }

        @media (max-width: 1024px) {
            .hide-tablet { display: none; }
            .show-tablet { display: block; }
        }
    </style>
</head>
<body>
    <div class="min-h-screen dark-bg">
        <!-- Top Navigation -->
        <nav class="header-dark shadow-lg border-b border-gray-600 backdrop-blur-sm bg-opacity-95 sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <!-- Logo and Navigation -->
                    <div class="flex items-center">
                        <!-- Logo -->
                        <div class="flex-shrink-0 flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-brain text-white text-sm"></i>
                            </div>
                            <h1 class="text-xl font-bold gradient-text">AI API Provider</h1>
                        </div>

                        <!-- Navigation Links -->
                        <div class="hidden lg:ml-10 lg:flex lg:items-center lg:space-x-1">
                            <a href="dashboard.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'dashboard.php') ? 'active' : ''; ?> px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                <i class="fas fa-tachometer-alt mr-2"></i>
                                Dashboard
                            </a>
                            <a href="docs.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'docs.php') ? 'active' : ''; ?> px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                <i class="fas fa-book mr-2"></i>
                                API Docs
                            </a>
                        </div>
                    </div>

                    <!-- Right side -->
                    <div class="flex items-center space-x-3">
                        <!-- Quick Actions -->
                        <div class="hidden md:flex items-center space-x-2">
                            <!-- API Status -->
                            <div class="flex items-center px-3 py-1.5 bg-green-900 bg-opacity-30 border border-green-500 rounded-lg">
                                <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                                <span class="text-xs font-medium text-green-400">API Online</span>
                            </div>

                            <!-- Notifications -->
                            <button class="relative p-2 text-muted hover:text-light rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-bell"></i>
                                <span class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                            </button>

                            <!-- Help -->
                            <button class="p-2 text-muted hover:text-light rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-question-circle"></i>
                            </button>
                        </div>

                        <!-- User Menu -->
                        <?php if (isset($user)): ?>
                        <div class="relative" id="user-dropdown">
                            <button id="user-dropdown-button" class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                                <div class="hidden md:block text-right">
                                    <p class="text-sm font-medium text-light">
                                        <?php echo htmlspecialchars(explode('@', $user['email'])[0]); ?>
                                    </p>
                                    <p class="text-xs text-muted">
                                        <?php echo ucfirst($user['plan']); ?> Plan
                                    </p>
                                </div>
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                                <i class="fas fa-chevron-down text-muted text-xs hidden md:block transition-transform" id="user-chevron"></i>
                            </button>

                            <!-- User Dropdown -->
                            <div id="user-dropdown-menu" class="absolute top-full right-0 mt-1 w-56 bg-gray-800 border border-gray-600 rounded-lg shadow-xl opacity-0 invisible transition-all duration-200 z-50">
                                <div class="px-4 py-3 border-b border-gray-600">
                                    <p class="text-sm font-medium text-light"><?php echo htmlspecialchars($user['email']); ?></p>
                                    <p class="text-xs text-muted"><?php echo ucfirst($user['plan']); ?> Plan</p>
                                </div>
                                <a href="dashboard.php" class="block px-4 py-2 text-sm text-muted hover:text-light hover:bg-gray-700">
                                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-muted hover:text-light hover:bg-gray-700">
                                    <i class="fas fa-user mr-2"></i>Profile
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-muted hover:text-light hover:bg-gray-700">
                                    <i class="fas fa-key mr-2"></i>API Keys
                                </a>
                                <a href="#" class="block px-4 py-2 text-sm text-muted hover:text-light hover:bg-gray-700">
                                    <i class="fas fa-credit-card mr-2"></i>Billing
                                </a>
                                <div class="border-t border-gray-600">
                                    <a href="logout.php" class="block px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-gray-700 rounded-b-lg">
                                        <i class="fas fa-sign-out-alt mr-2"></i>Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="flex items-center space-x-3">
                            <a href="login.php" class="text-sm font-medium text-muted hover:text-light transition-colors">
                                Sign In
                            </a>
                            <a href="register.php" class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:opacity-90 transition-opacity">
                                Get Started
                            </a>
                        </div>
                        <?php endif; ?>

                        <!-- Mobile menu button -->
                        <div class="lg:hidden">
                            <button type="button" class="p-2 text-muted hover:text-light rounded-lg hover:bg-gray-700 transition-colors" onclick="toggleMobileMenu()">
                                <i class="fas fa-bars"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mobile Navigation Menu -->
                <div class="lg:hidden hidden" id="mobile-menu">
                    <div class="px-4 pt-4 pb-6 space-y-2 border-t border-gray-600 bg-gray-800 bg-opacity-95 backdrop-blur-sm">
                        <!-- Navigation Links -->
                        <a href="dashboard.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'dashboard.php') ? 'active' : ''; ?> flex items-center px-4 py-3 rounded-lg text-sm font-medium">
                            <i class="fas fa-tachometer-alt mr-3 w-5"></i>
                            Dashboard
                        </a>
                        <a href="docs.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'docs.php') ? 'active' : ''; ?> flex items-center px-4 py-3 rounded-lg text-sm font-medium">
                            <i class="fas fa-book mr-3 w-5"></i>
                            API Documentation
                        </a>

                        <?php if (isset($user)): ?>
                        <!-- Mobile User Section -->
                        <div class="border-t border-gray-600 pt-4 mt-4">
                            <div class="flex items-center px-4 py-3 mb-2">
                                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-light"><?php echo htmlspecialchars(explode('@', $user['email'])[0]); ?></p>
                                    <p class="text-xs text-muted"><?php echo ucfirst($user['plan']); ?> Plan</p>
                                </div>
                            </div>

                            <!-- API Status Mobile -->
                            <div class="flex items-center justify-between px-4 py-2 mb-2">
                                <span class="text-sm text-muted">API Status</span>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                                    <span class="text-xs font-medium text-green-400">Online</span>
                                </div>
                            </div>

                            <a href="logout.php" class="nav-link flex items-center px-4 py-3 rounded-lg text-sm font-medium text-red-400 hover:text-red-300">
                                <i class="fas fa-sign-out-alt mr-3 w-5"></i>
                                Sign Out
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Header -->
        <?php if (isset($pageHeader) && $pageHeader): ?>
        <header class="bg-dark-card border-b border-gray-600">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div>
                    <h1 class="text-2xl font-semibold text-light"><?php echo isset($pageTitle) ? $pageTitle : 'Dashboard'; ?></h1>
                    <p class="text-sm text-muted"><?php echo isset($pageDescription) ? $pageDescription : 'Welcome back!'; ?></p>
                </div>
            </div>
        </header>
        <?php endif; ?>

        <!-- Main Content Area -->
        <main class="dark-bg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
