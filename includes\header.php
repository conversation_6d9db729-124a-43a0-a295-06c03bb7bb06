<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle : 'AI API Provider'; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1d29;
            color: #e2e8f0;
        }
        .dark-bg {
            background: #1a1d29;
        }
        .sidebar {
            background: #252a3d;
            border-right: 1px solid #2d3748;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
        }
        .main-content {
            background: #1a1d29;
            min-height: 100vh;
        }
        .card {
            background: #252a3d;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            border: 1px solid #2d3748;
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            transform: translateY(-2px);
        }
        .stat-card {
            background: #252a3d;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #2d3748;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
        }
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .sidebar-link {
            transition: all 0.3s ease;
            border-radius: 8px;
            color: #a0aec0;
            text-decoration: none;
        }
        .sidebar-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #e2e8f0;
        }
        .sidebar-link.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        .nav-container {
            background: #252a3d;
            border-bottom: 1px solid #2d3748;
        }
        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 6px;
            height: 8px;
        }
        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .badge-success { background: rgba(16, 185, 129, 0.2); color: #10b981; border: 1px solid #10b981; }
        .badge-warning { background: rgba(245, 158, 11, 0.2); color: #f59e0b; border: 1px solid #f59e0b; }
        .badge-info { background: rgba(59, 130, 246, 0.2); color: #3b82f6; border: 1px solid #3b82f6; }
        .badge-purple { background: rgba(139, 92, 246, 0.2); color: #8b5cf6; border: 1px solid #8b5cf6; }
        .table-row {
            transition: all 0.2s ease;
        }
        .table-row:hover {
            background: rgba(102, 126, 234, 0.1);
        }
        .chart-container {
            background: #252a3d;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #2d3748;
        }
        .mini-chart {
            width: 60px;
            height: 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        .mini-chart::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%);
        }
        .header-dark {
            background: #252a3d;
            border-bottom: 1px solid #2d3748;
        }
        .text-light { color: #e2e8f0; }
        .text-muted { color: #a0aec0; }
        .text-success { color: #10b981; }
        .text-warning { color: #f59e0b; }
        .text-info { color: #3b82f6; }
        .bg-dark-card { background: #252a3d; }
        .border-dark { border-color: #2d3748; }
    </style>
</head>
<body>
    <div class="min-h-screen dark-bg">
        <!-- Top Navigation -->
        <nav class="header-dark shadow-sm border-b border-gray-600">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <!-- Logo and Navigation -->
                    <div class="flex items-center">
                        <!-- Logo -->
                        <div class="flex-shrink-0">
                            <h1 class="text-xl font-bold text-light">AI API Provider</h1>
                        </div>
                        
                        <!-- Navigation Links -->
                        <div class="hidden md:ml-10 md:flex md:items-baseline md:space-x-8">
                            <a href="dashboard.php" class="sidebar-link <?php echo (basename($_SERVER['PHP_SELF']) == 'dashboard.php') ? 'active' : ''; ?> px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-tachometer-alt mr-2"></i>
                                Dashboard
                            </a>
                            <a href="docs.php" class="sidebar-link <?php echo (basename($_SERVER['PHP_SELF']) == 'docs.php') ? 'active' : ''; ?> px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-book mr-2"></i>
                                API Docs
                            </a>
                            <a href="#" class="sidebar-link px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-chart-bar mr-2"></i>
                                Analytics
                            </a>
                            <a href="#" class="sidebar-link px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-cog mr-2"></i>
                                Settings
                            </a>
                            <a href="#" class="sidebar-link px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-credit-card mr-2"></i>
                                Billing
                            </a>
                        </div>
                    </div>

                    <!-- Right side -->
                    <div class="flex items-center space-x-4">
                        <!-- Notifications -->
                        <button class="p-2 text-muted hover:text-light rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-bell"></i>
                        </button>
                        
                        <!-- User Menu -->
                        <?php if (isset($user)): ?>
                        <div class="flex items-center space-x-3">
                            <div class="hidden md:block text-right">
                                <p class="text-sm font-medium text-light">
                                    <?php echo htmlspecialchars($user['email']); ?>
                                </p>
                                <p class="text-xs text-muted">
                                    <?php echo ucfirst($user['plan']); ?> Plan
                                </p>
                            </div>
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <a href="logout.php" class="text-muted hover:text-light transition-colors p-2">
                                <i class="fas fa-sign-out-alt"></i>
                            </a>
                        </div>
                        <?php endif; ?>

                        <!-- Mobile menu button -->
                        <div class="md:hidden">
                            <button type="button" class="p-2 text-muted hover:text-light rounded-lg hover:bg-gray-700 transition-colors" onclick="toggleMobileMenu()">
                                <i class="fas fa-bars"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Mobile Navigation Menu -->
                <div class="md:hidden hidden" id="mobile-menu">
                    <div class="px-2 pt-2 pb-3 space-y-1 border-t border-gray-600">
                        <a href="dashboard.php" class="sidebar-link <?php echo (basename($_SERVER['PHP_SELF']) == 'dashboard.php') ? 'active' : ''; ?> block px-3 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-tachometer-alt mr-2"></i>
                            Dashboard
                        </a>
                        <a href="docs.php" class="sidebar-link <?php echo (basename($_SERVER['PHP_SELF']) == 'docs.php') ? 'active' : ''; ?> block px-3 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-book mr-2"></i>
                            API Documentation
                        </a>
                        <a href="#" class="sidebar-link block px-3 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-chart-bar mr-2"></i>
                            Analytics
                        </a>
                        <a href="#" class="sidebar-link block px-3 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-cog mr-2"></i>
                            Settings
                        </a>
                        <a href="#" class="sidebar-link block px-3 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-credit-card mr-2"></i>
                            Billing
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Header -->
        <?php if (isset($pageHeader) && $pageHeader): ?>
        <header class="bg-dark-card border-b border-gray-600">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div>
                    <h1 class="text-2xl font-semibold text-light"><?php echo isset($pageTitle) ? $pageTitle : 'Dashboard'; ?></h1>
                    <p class="text-sm text-muted"><?php echo isset($pageDescription) ? $pageDescription : 'Welcome back!'; ?></p>
                </div>
            </div>
        </header>
        <?php endif; ?>

        <!-- Main Content Area -->
        <main class="dark-bg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
