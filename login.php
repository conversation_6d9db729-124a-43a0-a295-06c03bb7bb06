<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'config/security.php';

initSecurityBeforeSession();
session_start();
initSecurityAfterSession();

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

if ($_POST) {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields.';
    } elseif (!validateEmail($email)) {
        $error = 'Please enter a valid email address.';
    } elseif (!checkLoginAttempts($email)) {
        $error = 'Too many login attempts. Please try again in 15 minutes.';
        logSecurityEvent('login_rate_limit', ['email' => $email]);
    } else {
        $user = getUserByEmail($email);

        if ($user && verifyPassword($password, $user['password'])) {
            recordLoginAttempt($email, true);
            $_SESSION['user_id'] = $user['id'];
            logSecurityEvent('login_success', ['user_id' => $user['id']]);
            header('Location: dashboard.php');
            exit;
        } else {
            recordLoginAttempt($email, false);
            logSecurityEvent('login_failed', ['email' => $email]);
            $error = 'Invalid email or password.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - AI API Provider</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .text-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-gray-100">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <h1 class="text-2xl font-bold text-gradient mb-2">AI API Provider</h1>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                    Sign in to your account
                </h2>
                <p class="text-sm text-gray-600">
                    Or
                    <a href="register.php" class="font-medium text-blue-600 hover:text-blue-500 transition-colors">
                        create a new account
                    </a>
                </p>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-8">
                <form class="space-y-6" method="POST">
                    <?php if ($error): ?>
                        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                            <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg text-sm">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>

                    <div class="space-y-4">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email address</label>
                            <input id="email" name="email" type="email" required
                                   class="w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                   placeholder="Enter your email" value="<?php echo htmlspecialchars($email ?? ''); ?>">
                        </div>
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <input id="password" name="password" type="password" required
                                   class="w-full px-3 py-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                   placeholder="Enter your password">
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input id="remember-me" name="remember-me" type="checkbox"
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="remember-me" class="ml-2 block text-sm text-gray-700">
                                Remember me
                            </label>
                        </div>

                        <div class="text-sm">
                            <a href="#" class="font-medium text-blue-600 hover:text-blue-500 transition-colors">
                                Forgot password?
                            </a>
                        </div>
                    </div>

                    <div>
                        <button type="submit"
                                class="w-full gradient-bg text-white py-2.5 px-4 rounded-lg text-sm font-medium hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-opacity">
                            Sign in
                        </button>
                    </div>

                    <div class="text-center">
                        <a href="index.php" class="text-sm font-medium text-gray-600 hover:text-gray-500 transition-colors">
                            ← Back to home
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
