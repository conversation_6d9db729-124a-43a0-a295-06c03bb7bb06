<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'config/security.php';

initSecurityBeforeSession();
session_start();
initSecurityAfterSession();

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

if ($_POST) {
    $email = sanitizeInput($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';

    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields.';
    } elseif (!validateEmail($email)) {
        $error = 'Please enter a valid email address.';
    } elseif (!checkLoginAttempts($email)) {
        $error = 'Too many login attempts. Please try again in 15 minutes.';
        logSecurityEvent('login_rate_limit', ['email' => $email]);
    } else {
        $user = getUserByEmail($email);

        if ($user && verifyPassword($password, $user['password'])) {
            recordLoginAttempt($email, true);
            $_SESSION['user_id'] = $user['id'];
            logSecurityEvent('login_success', ['user_id' => $user['id']]);
            header('Location: dashboard.php');
            exit;
        } else {
            recordLoginAttempt($email, false);
            logSecurityEvent('login_failed', ['email' => $email]);
            $error = 'Invalid email or password.';
        }
    }
}

// Set page variables for header
$pageTitle = 'Login - AI API Provider';
$pageHeader = false; // No page header for auth pages

// Include header
require_once 'includes/header.php';
?>

<!-- Additional styles for auth pages -->
<style>
    .auth-card {
        background: #252a3d;
        border: 1px solid #2d3748;
        border-radius: 12px;
        padding: 32px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4);
    }
    .auth-input {
        background: #1a1d29;
        border: 1px solid #2d3748;
        color: #e2e8f0;
    }
    .auth-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    .auth-input::placeholder {
        color: #a0aec0;
    }
</style>

<div class="min-h-screen flex items-center justify-center padding-responsive">
    <div class="container-responsive max-w-md w-full space-responsive">
        <div class="text-center">
            <h1 class="text-responsive-xl font-bold gradient-text mb-2">AI API Provider</h1>
            <h2 class="text-responsive-xl font-bold text-light mb-2">
                Sign in to your account
            </h2>
            <p class="text-responsive-sm text-muted">
                Or
                <a href="register.php" class="font-medium text-blue-400 hover:text-blue-300 transition-colors">
                    create a new account
                </a>
            </p>
        </div>

        <div class="auth-card card-responsive">
            <form class="form-responsive space-y-6" method="POST">
                <?php if ($error): ?>
                    <div class="bg-red-900 bg-opacity-20 border border-red-500 text-red-400 padding-responsive-sm rounded-lg text-responsive-sm">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="bg-green-900 bg-opacity-20 border border-green-500 text-green-400 padding-responsive-sm rounded-lg text-responsive-sm">
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>

                <div class="space-y-4">
                    <div>
                        <label for="email" class="block text-responsive-sm font-medium text-light mb-1">Email address</label>
                        <input id="email" name="email" type="email" required
                               class="auth-input w-full px-3 py-2.5 rounded-lg text-responsive-sm focus:outline-none transition-all"
                               placeholder="Enter your email" value="<?php echo htmlspecialchars($email ?? ''); ?>">
                    </div>
                    <div>
                        <label for="password" class="block text-responsive-sm font-medium text-light mb-1">Password</label>
                        <input id="password" name="password" type="password" required
                               class="auth-input w-full px-3 py-2.5 rounded-lg text-responsive-sm focus:outline-none transition-all"
                               placeholder="Enter your password">
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-600 rounded bg-gray-700">
                        <label for="remember-me" class="ml-2 block text-sm text-muted">
                            Remember me
                        </label>
                    </div>

                    <div class="text-sm">
                        <a href="#" class="font-medium text-blue-400 hover:text-blue-300 transition-colors">
                            Forgot password?
                        </a>
                    </div>
                </div>

                <div>
                    <button type="submit"
                            class="btn-responsive w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-opacity">
                        Sign in
                    </button>
                </div>

                <div class="text-center">
                    <a href="index.php" class="text-responsive-sm font-medium text-muted hover:text-light transition-colors">
                        ← Back to home
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
