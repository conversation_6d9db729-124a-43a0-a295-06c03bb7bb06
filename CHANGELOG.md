# Changelog - OpenAI API Provider

## [Updated] - 2024-01-XX

### 🐛 Bug Fixes
- **Fixed PHP Session Warnings**: Resolved "Session ini settings cannot be changed when a session is active" warnings
  - Restructured security initialization to set session parameters before `session_start()`
  - Split `initSecurity()` into `initSecurityBeforeSession()` and `initSecurityAfterSession()`
  - Updated all PHP files to use proper initialization order
  - All security features remain fully functional

- **Fixed API Endpoint Fatal Error**: Resolved "Call to undefined function getApiErrorResponse()" error
  - Moved all `require_once` statements to the top of `api/v1/chat/completions.php`
  - Functions are now available before being called
  - API endpoint now returns proper JSON error responses

- **Fixed Token Calculation TypeError**: Resolved "strlen(): Argument #1 must be of type string, array given" error
  - Enhanced token calculation to handle both string and array content formats
  - Added support for multimodal content (text + images)
  - API now properly processes modern AI content formats

### 🔄 Changed Terminology
- **Updated all references from "requests/month" to "user messages per month"**
- This change provides clearer understanding for users about what they're being charged for

### 📄 Files Updated:

#### Frontend Pages:
- **index.php**: Updated all pricing plan descriptions
  - Basic: "100 user messages per month"
  - Pro: "500 user messages per month"
  - Developer: "2,500 user messages per month"
  - Enterprise: "10,000 user messages per month"

- **register.php**: Updated plan selection dropdown options
- **dashboard.php**:
  - Changed "Requests Used" to "Messages Used"
  - Updated "Monthly Usage" to "Monthly Message Usage"
  - Updated rate limit warning message
  - Updated plan upgrade section descriptions

- **docs.php**:
  - Updated Quick Start section
  - Updated Rate Limits section with new terminology
  - Updated all plan descriptions

#### Backend:
- **api/v1/chat/completions.php**: Updated error message for rate limiting
  - Changed: "Rate limit exceeded. Upgrade your plan for more requests."
  - To: "Rate limit exceeded. Upgrade your plan for more user messages."

#### Documentation:
- **README.md**: Updated pricing table header and descriptions
- **SETUP_GUIDE.md**: Updated all references to use "user messages/bulan"
- **includes/functions.php**: Updated function comments for clarity

### 🎯 Impact:
- **User Experience**: Clearer understanding of what counts toward their monthly limit
- **Consistency**: All UI elements now use consistent terminology
- **Documentation**: All docs reflect the new messaging terminology
- **API Responses**: Error messages are more user-friendly

### ✅ What This Means:
- Each user message sent to the API counts as 1 "user message"
- Monthly limits are now expressed as "user messages per month" instead of "requests per month"
- This provides better clarity for users about their usage patterns
- All functionality remains the same - only the terminology has been updated

### 🔧 Technical Details:
- No database schema changes required
- No API endpoint changes required
- No breaking changes to existing integrations
- All existing API keys continue to work
- Rate limiting logic remains unchanged

### 📊 Updated Pricing Display:
| Plan | Price | User Messages/Month | Features |
|------|-------|---------------------|----------|
| Basic | Free | 100 | All models, API docs |
| Pro | $20/month | 500 | Priority support |
| Developer | $50/month | 2,500 | Advanced analytics |
| Enterprise | $150/month | 10,000 | Dedicated support |

### 🚀 Next Steps:
- Monitor user feedback on the new terminology
- Consider adding tooltips to explain what counts as a "user message"
- Update any external documentation or marketing materials
- Consider adding usage examples in the documentation

---

**Note**: This update improves user understanding without changing any core functionality. All existing users and integrations continue to work exactly as before.
