# AI API Provider

A premium AI API service that provides access to multiple AI models including <PERSON>, GP<PERSON>, <PERSON>, and more with competitive pricing and easy integration.

## Features

- **Multiple AI Models**: Access to Claude 3.5/3.7/4, GPT-4o, O1/O3/O4, Qwen, DeepSeek, Grok, Gemini, and more
- **Flexible Pricing**: From free tier (100 requests/month) to enterprise (10,000 requests/month)
- **Easy Integration**: OpenAI-compatible API format
- **Real-time Analytics**: Track usage, tokens, and performance
- **Secure**: API key authentication and rate limiting
- **Model Mapping**: Transparent model name mapping to actual providers

## Pricing Plans

| Plan | Price | User Messages/Month | Features |
|------|-------|---------------------|----------|
| Basic | Free | 100 | All models, API docs |
| Pro | $20/month | 500 | Priority support |
| Developer | $50/month | 2,500 | Advanced analytics |
| Enterprise | $150/month | 10,000 | Dedicated support |

## Quick Start

### 1. Installation

1. Clone this repository to your XAMPP htdocs folder:
```bash
cd c:\xampp\htdocs
git clone <repository-url> openai-api-provider
cd openai-api-provider
```

2. Start XAMPP and ensure Apache and MySQL are running

3. Open your browser and navigate to `http://localhost/openai-api-provider`

4. The database will be automatically created on first visit

### 2. Get Your API Key

1. Register for an account at `http://localhost/openai-api-provider/register.php`
2. Choose your plan (start with Basic for free - 100 user messages/month)
3. Get your API key from the dashboard

### 3. Make Your First Request

```bash
curl -X POST http://localhost/openai-api-provider/api/v1/chat/completions \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3.5-sonnet",
    "messages": [
      {
        "role": "user",
        "content": "Hello! How are you?"
      }
    ]
  }'
```

## Supported Models

| Display Name | Actual Model | Provider |
|--------------|--------------|----------|
| claude-3.5-sonnet | claude-3-5-sonnet-******** | Anthropic |
| claude-3.7-sonnet | claude-3-7-sonnet-******** | Anthropic |
| claude-4-sonnet | anthropic/claude-sonnet-4 | Anthropic |
| qwen-2.5-72b-instruct-turbo | Qwen/Qwen2.5-72B-Instruct-Turbo | Alibaba |
| qwen-2.5-coder-32b-instruct | Qwen/Qwen2.5-Coder-32B-Instruct | Alibaba |
| llama-4-maverick-17b | llama-4-maverick-17b-128e-instruct-fp8 | Meta |
| deepseek-r1 | deepseek-r1 | DeepSeek |
| deepseek-chat | deepseek/deepseek-chat | DeepSeek |
| deepseek-v3 | deepseek-v3 | DeepSeek |
| o1 | o1 | OpenAI |
| o3-mini | o3-mini | OpenAI |
| gpt-4o-mini | gpt-4o-mini | OpenAI |
| o4-mini | o4-mini | OpenAI |
| grok-3-beta | grok-3-beta | xAI |
| gemini-2.5-pro | gemini-2.5-pro-preview-03-25 | Google |

## API Documentation

### Authentication

All requests must include your API key in the Authorization header:

```
Authorization: Bearer YOUR_API_KEY
```

### Chat Completions

**Endpoint:** `POST /api/v1/chat/completions`

**Request Body:**
```json
{
  "model": "claude-3.5-sonnet",
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "Hello!"
    }
  ],
  "max_tokens": 1000,
  "temperature": 0.7,
  "stream": false
}
```

**Response:**
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "claude-3.5-sonnet",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm doing well, thank you for asking. How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 12,
    "total_tokens": 21
  }
}
```

## Configuration

### Database Configuration

Edit `config/database.php` to change database settings:

```php
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'openai_api_provider');
```

### Upstream API Configuration

The system proxies requests to `https://oi-vscode-server-5.onrender.com/v1/chat/completions` with the following headers:

- Authorization: Bearer xxx
- CustomerID: cus_SBLPR2vIB7Tp4z
- User-Agent: Pr/JS 4.73.1
- Various X-Stainless headers for compatibility

## Database Schema

### Users Table
- `id` - Primary key
- `email` - User email (unique)
- `password` - Hashed password
- `plan` - Subscription plan (basic, pro, developer, enterprise)
- `api_key` - Unique API key
- `requests_used` - Current month usage
- `requests_limit` - Monthly limit based on plan
- `created_at` - Account creation timestamp

### API Requests Table
- `id` - Primary key
- `user_id` - Foreign key to users
- `model_requested` - Model name requested by user
- `model_actual` - Actual model name sent to upstream
- `tokens_used` - Tokens consumed
- `response_time` - Request duration
- `status_code` - HTTP response code
- `created_at` - Request timestamp

## Security Features

- Password hashing with PHP's `password_hash()`
- API key validation and authentication
- Rate limiting based on subscription plans
- SQL injection protection with prepared statements
- XSS protection with input sanitization
- CORS headers for API access
- Secure session management

## Error Handling

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid request format |
| 401 | Unauthorized - Invalid API key |
| 429 | Rate Limit Exceeded - Upgrade plan needed |
| 500 | Internal Server Error |
| 502 | Bad Gateway - Upstream API error |

## Development

### File Structure
```
/
├── api/v1/chat/completions.php  # Main API endpoint
├── config/database.php          # Database configuration
├── includes/functions.php       # Helper functions
├── index.php                    # Landing page
├── login.php                    # User login
├── register.php                 # User registration
├── dashboard.php                # User dashboard
├── docs.php                     # API documentation
├── logout.php                   # Logout handler
├── .htaccess                    # Apache configuration
└── README.md                    # This file
```

### Adding New Models

1. Update the model mapping in `config/database.php`:
```php
function getModelMapping() {
    return [
        'your-model-name' => 'actual-upstream-model-name',
        // ... existing models
    ];
}
```

2. Update the documentation in `docs.php` to include the new model

### Customizing Plans

Edit the plan limits in `config/database.php`:
```php
function getPlanLimits() {
    return [
        'basic' => ['limit' => 100, 'price' => 0],
        'custom' => ['limit' => 1000, 'price' => 10],
        // ... other plans
    ];
}
```

## Support

For support and questions:
- Check the API documentation at `/docs.php`
- Review error codes and troubleshooting
- Contact support through the dashboard

## License

This project is proprietary software. All rights reserved.
