# OpenAI API Provider - Setup Guide

## 🎉 Website Berhasil Dibuat!

Website penyedia OpenAI API dengan sistem pricing dan proxy telah berhasil dibuat dengan fitur-fitur lengkap sesuai permintaan Anda.

## 📁 Struktur File

```
openai-api-provider/
├── 📄 index.php                    # Landing page utama
├── 📄 login.php                    # Halaman login
├── 📄 register.php                 # Halaman registrasi
├── 📄 dashboard.php                # Dashboard user
├── 📄 docs.php                     # Dokumentasi API
├── 📄 logout.php                   # Handler logout
├── 📄 404.php                      # Error page 404
├── 📄 500.php                      # Error page 500
├── 📄 test_api.php                 # Test script untuk API
├── 📄 test_upstream.php            # Test koneksi upstream
├── 📄 database.sql                 # SQL setup manual
├── 📄 .htaccess                    # Konfigurasi Apache
├── 📄 README.md                    # Dokumentasi lengkap
├── 📁 api/v1/chat/
│   └── 📄 completions.php          # Endpoint API utama
├── 📁 config/
│   ├── 📄 database.php             # Konfigurasi database
│   └── 📄 security.php             # Fitur keamanan
└── 📁 includes/
    └── 📄 functions.php            # Helper functions
```

## 🚀 Cara Menggunakan

### 1. Akses Website
Buka browser dan kunjungi: `http://localhost/`

### 2. Registrasi Akun
- Klik "Sign Up" atau kunjungi `http://localhost/register.php`
- Pilih plan yang diinginkan (Basic gratis dengan 100 request/bulan)
- Setelah registrasi, Anda akan mendapat API key

### 3. Gunakan API
```bash
curl -X POST http://localhost/api/v1/chat/completions \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3.5-sonnet",
    "messages": [
      {
        "role": "user",
        "content": "Hello!"
      }
    ]
  }'
```

## 💰 Pricing Plans

| Plan | Harga | Request/Bulan | Fitur |
|------|-------|---------------|-------|
| **Basic** | Gratis | 100 | Semua model AI |
| **Pro** | $20/bulan | 500 | Priority support |
| **Developer** | $50/bulan | 2,500 | Advanced analytics |
| **Enterprise** | $150/bulan | 10,000 | Dedicated support |

## 🤖 Model yang Didukung

| Nama Display | Model Asli | Provider |
|--------------|------------|----------|
| claude-3.5-sonnet | claude-3-5-sonnet-20241022 | Anthropic |
| claude-3.7-sonnet | claude-3-7-sonnet-20250219 | Anthropic |
| claude-4-sonnet | anthropic/claude-sonnet-4 | Anthropic |
| qwen-2.5-72b-instruct-turbo | Qwen/Qwen2.5-72B-Instruct-Turbo | Alibaba |
| qwen-2.5-coder-32b-instruct | Qwen/Qwen2.5-Coder-32B-Instruct | Alibaba |
| llama-4-maverick-17b | llama-4-maverick-17b-128e-instruct-fp8 | Meta |
| deepseek-r1 | deepseek-r1 | DeepSeek |
| deepseek-chat | deepseek/deepseek-chat | DeepSeek |
| deepseek-v3 | deepseek-v3 | DeepSeek |
| o1 | o1 | OpenAI |
| o3-mini | o3-mini | OpenAI |
| gpt-4o-mini | gpt-4o-mini | OpenAI |
| o4-mini | o4-mini | OpenAI |
| grok-3-beta | grok-3-beta | xAI |
| gemini-2.5-pro | gemini-2.5-pro-preview-03-25 | Google |

## 🔧 Fitur Utama

### ✅ Frontend
- **Landing page** dengan pricing yang menarik
- **Dashboard** untuk monitoring usage
- **Dokumentasi API** yang lengkap
- **Sistem autentikasi** yang aman
- **Responsive design** dengan Tailwind CSS

### ✅ Backend
- **API proxy** ke `https://oi-vscode-server-5.onrender.com/v1/chat/completions`
- **Rate limiting** berdasarkan plan
- **Model mapping** otomatis
- **Database logging** untuk analytics
- **Sistem keamanan** berlapis

### ✅ Database
- **Auto-setup** database dan tabel
- **User management** dengan hashed password
- **API key generation** yang aman
- **Request logging** untuk monitoring
- **Payment tracking** (siap untuk integrasi)

### ✅ Keamanan
- **CSRF protection**
- **Rate limiting** untuk login
- **API key validation**
- **XSS protection**
- **SQL injection prevention**
- **Security logging**

## 🧪 Testing

### Test Database & Functions
Kunjungi: `http://localhost/test_api.php`

### Test Upstream Connection
Kunjungi: `http://localhost/test_upstream.php`

## 📊 Monitoring

### Dashboard User
- Usage statistics
- Request history
- Plan management
- API key management

### Admin Features (dapat dikembangkan)
- User analytics
- Revenue tracking
- System monitoring
- Security logs

## 🔒 Keamanan

- **Password hashing** dengan bcrypt
- **API key format** validation
- **Rate limiting** per user dan IP
- **Security headers** untuk XSS/CSRF protection
- **Input sanitization** untuk semua form
- **Audit logging** untuk security events

## 🌐 API Endpoint

**Base URL:** `http://localhost/api/v1/`

**Endpoint:** `POST /chat/completions`

**Headers:**
```
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

**Request Body:**
```json
{
  "model": "claude-3.5-sonnet",
  "messages": [
    {"role": "user", "content": "Hello!"}
  ],
  "max_tokens": 1000,
  "temperature": 0.7
}
```

## 🔄 Proxy Configuration

API akan meneruskan request ke:
- **URL:** `https://oi-vscode-server-5.onrender.com/v1/chat/completions`
- **Headers:** Sesuai spesifikasi (Authorization: Bearer xxx, CustomerID, dll)
- **Model mapping:** Otomatis dari display name ke actual name

## 📈 Scalability

Website ini siap untuk:
- **Payment integration** (Stripe, PayPal)
- **Admin dashboard** untuk management
- **API analytics** yang lebih detail
- **Multi-tenant** support
- **Load balancing** untuk high traffic

## 🎯 Next Steps

1. **Customize branding** sesuai kebutuhan
2. **Integrate payment gateway** untuk plan berbayar
3. **Add admin dashboard** untuk management
4. **Implement email notifications**
5. **Add API documentation** yang lebih detail
6. **Setup monitoring** dan alerting

## 📞 Support

Jika ada pertanyaan atau butuh bantuan:
1. Cek dokumentasi di `/docs.php`
2. Review error logs di `/logs/`
3. Test API dengan `/test_api.php`
4. Periksa database dengan phpMyAdmin

---

**🎉 Selamat! Website OpenAI API Provider Anda sudah siap digunakan!**
