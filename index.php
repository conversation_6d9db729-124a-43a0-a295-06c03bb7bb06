<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'config/security.php';

initSecurityBeforeSession();
session_start();
initSecurityAfterSession();

// Check if user is logged in
$user = null;
if (isset($_SESSION['user_id'])) {
    $user = getUserById($_SESSION['user_id']);
}

// Set page variables for header
$pageTitle = 'AI API Provider - Premium AI Models';
$pageHeader = false; // No page header for landing page

// Include header
require_once 'includes/header.php';
?>

<!-- Enhanced styles for premium landing page -->
<style>
    .hero-gradient {
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
        position: relative;
        overflow: hidden;
    }
    .hero-gradient::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
        animation: float 6s ease-in-out infinite;
    }
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }
    .floating-particles {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
    .floating-particles::before,
    .floating-particles::after {
        content: '';
        position: absolute;
        width: 4px;
        height: 4px;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 50%;
        animation: float-particles 8s linear infinite;
    }
    .floating-particles::before {
        left: 20%;
        animation-delay: 0s;
    }
    .floating-particles::after {
        left: 80%;
        animation-delay: 4s;
    }
    @keyframes float-particles {
        0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
        10% { opacity: 1; }
        90% { opacity: 1; }
        100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
    }
    .card-hover {
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
    }
    .card-hover::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s;
    }
    .card-hover:hover::before {
        left: 100%;
    }
    .card-hover:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    }
    .pricing-card {
        background: linear-gradient(145deg, #252a3d, #1e2332);
        border: 1px solid #2d3748;
        border-radius: 16px;
        padding: 32px;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
    }
    .pricing-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }
    .pricing-card:hover::before {
        transform: scaleX(1);
    }
    .pricing-card:hover {
        transform: translateY(-12px);
        box-shadow: 0 30px 60px -12px rgba(0, 0, 0, 0.6);
        border-color: #667eea;
    }
    .pricing-card.featured {
        border: 2px solid #667eea;
        transform: scale(1.05);
        background: linear-gradient(145deg, #2a2f4a, #252a3d);
        box-shadow: 0 20px 40px -8px rgba(102, 126, 234, 0.3);
    }
    .pricing-card.featured::before {
        transform: scaleX(1);
    }
    .model-card {
        background: linear-gradient(145deg, #252a3d, #1e2332);
        border: 1px solid #2d3748;
        border-radius: 16px;
        padding: 28px;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
    }
    .model-card::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 20px 20px 0;
        border-color: transparent #667eea transparent transparent;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    .model-card:hover::after {
        opacity: 1;
    }
    .model-card:hover {
        border-color: #667eea;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        transform: translateY(-6px);
    }
    .feature-icon {
        background: linear-gradient(135deg, #667eea, #764ba2);
        background-size: 200% 200%;
        animation: gradient-shift 3s ease infinite;
    }
    @keyframes gradient-shift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    .trust-indicator {
        transition: all 0.3s ease;
    }
    .trust-indicator:hover {
        transform: scale(1.1);
    }
    .cta-button {
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    .cta-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }
    .cta-button:hover::before {
        left: 100%;
    }
    .demo-code {
        background: linear-gradient(145deg, #1a1d29, #252a3d);
        border: 1px solid #2d3748;
    }
</style>

<!-- Hero Section -->
<div class="hero-gradient text-white relative overflow-hidden min-h-screen flex items-center">
    <!-- Floating Particles -->
    <div class="floating-particles"></div>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
            <!-- Announcement Badge -->
            <div class="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white text-sm font-medium mb-8 hover:bg-white/15 transition-all duration-300">
                <i class="fas fa-rocket mr-2 text-blue-300"></i>
                <span class="mr-2">🎉 Now Supporting</span>
                <span class="font-bold text-blue-300">Claude 3.5 Sonnet</span>
                <span class="mx-2">•</span>
                <span class="font-bold text-green-300">GPT-4o</span>
                <span class="mx-2">•</span>
                <span class="font-bold text-purple-300">Gemini Pro</span>
            </div>

            <!-- Main Heading -->
            <h1 class="text-4xl sm:text-6xl lg:text-8xl font-bold mb-8 leading-tight">
                <span class="block">AI API for</span>
                <span class="block gradient-text">Everyone</span>
            </h1>

            <!-- Subtitle -->
            <p class="text-xl sm:text-2xl mb-6 text-white/90 max-w-4xl mx-auto font-light">
                The most <span class="font-bold text-blue-300">developer-friendly</span> AI API platform
            </p>
            <p class="text-lg mb-12 text-white/70 max-w-3xl mx-auto leading-relaxed">
                Access cutting-edge AI models through our unified API. Simple integration, enterprise-grade reliability, transparent pricing, and world-class developer experience.
            </p>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
                <a href="register.php" class="group cta-button bg-gradient-to-r from-blue-500 to-purple-600 text-white px-10 py-4 rounded-xl text-lg font-semibold hover:shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:-translate-y-2">
                    <i class="fas fa-play mr-3"></i>
                    Start Building Free
                    <i class="fas fa-arrow-right ml-3 group-hover:translate-x-1 transition-transform"></i>
                </a>
                <a href="docs.php" class="group cta-button border-2 border-white/30 text-white px-10 py-4 rounded-xl text-lg font-semibold hover:bg-white hover:text-gray-900 transition-all duration-300 backdrop-blur-sm">
                    <i class="fas fa-book mr-3"></i>
                    View Documentation
                </a>
            </div>

            <!-- Trust Indicators -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center mb-16">
                <div class="trust-indicator group">
                    <div class="text-4xl font-bold text-white mb-2 group-hover:text-blue-300 transition-colors">99.9%</div>
                    <div class="text-white/60 text-sm">API Uptime</div>
                </div>
                <div class="trust-indicator group">
                    <div class="text-4xl font-bold text-white mb-2 group-hover:text-purple-300 transition-colors">50M+</div>
                    <div class="text-white/60 text-sm">API Requests</div>
                </div>
                <div class="trust-indicator group">
                    <div class="text-4xl font-bold text-white mb-2 group-hover:text-green-300 transition-colors">2,500+</div>
                    <div class="text-white/60 text-sm">Active Developers</div>
                </div>
                <div class="trust-indicator group">
                    <div class="text-4xl font-bold text-white mb-2 group-hover:text-yellow-300 transition-colors">24/7</div>
                    <div class="text-white/60 text-sm">Expert Support</div>
                </div>
            </div>

            <!-- Quick Demo -->
            <div class="max-w-4xl mx-auto">
                <div class="demo-code backdrop-blur-sm border border-white/20 rounded-2xl p-8 text-left">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <div class="flex space-x-2">
                                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            </div>
                            <span class="ml-4 text-white/70 text-sm font-medium">Quick Start Example</span>
                        </div>
                        <button class="text-white/70 hover:text-white transition-colors group" onclick="copyCode()">
                            <i class="fas fa-copy mr-2"></i>
                            <span class="text-sm">Copy</span>
                        </button>
                    </div>
                    <pre class="text-sm overflow-x-auto"><code class="text-green-300" id="demo-code">curl -X POST https://api.yourdomain.com/v1/chat/completions \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3.5-sonnet",
    "messages": [{"role": "user", "content": "Hello, AI!"}],
    "stream_options": {"include_usage": true}
  }'</code></pre>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pricing Section -->
<div class="py-20 dark-bg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-2xl md:text-3xl font-bold text-light mb-4">Choose Your Plan</h2>
            <p class="text-lg text-muted max-w-2xl mx-auto">Flexible pricing for every need, from individual developers to enterprise teams</p>
        </div>

        <div class="grid md:grid-cols-4 gap-6">
            <!-- Basic Plan -->
            <div class="pricing-card">
                <div class="text-center">
                    <h3 class="text-lg font-bold text-light mb-3">Basic</h3>
                    <div class="text-3xl font-bold text-light mb-2">Free</div>
                    <p class="text-sm text-muted mb-6">Perfect for testing</p>
                    <ul class="text-left space-y-2 mb-6 text-sm">
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">100 user messages per month</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">All AI models</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">API documentation</span></li>
                    </ul>
                    <a href="register.php" class="w-full bg-gray-600 text-white py-2.5 px-4 rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors block text-center">
                        Get Started
                    </a>
                </div>
            </div>

            <!-- Pro Plan -->
            <div class="pricing-card featured relative">
                <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <span class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-1 rounded-full text-xs font-medium">Popular</span>
                </div>
                <div class="text-center">
                    <h3 class="text-lg font-bold text-light mb-3">Pro</h3>
                    <div class="text-3xl font-bold text-light mb-2">$20</div>
                    <p class="text-sm text-muted mb-6">per month</p>
                    <ul class="text-left space-y-2 mb-6 text-sm">
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">500 user messages per month</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">All AI models</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">Priority support</span></li>
                    </ul>
                    <a href="register.php" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-2.5 px-4 rounded-lg text-sm font-medium hover:opacity-90 transition-opacity block text-center">
                        Choose Pro
                    </a>
                </div>
            </div>

            <!-- Developer Plan -->
            <div class="pricing-card">
                <div class="text-center">
                    <h3 class="text-lg font-bold text-light mb-3">Developer</h3>
                    <div class="text-3xl font-bold text-light mb-2">$50</div>
                    <p class="text-sm text-muted mb-6">per month</p>
                    <ul class="text-left space-y-2 mb-6 text-sm">
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">2,500 user messages per month</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">All AI models</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">Advanced analytics</span></li>
                    </ul>
                    <a href="register.php" class="w-full bg-purple-600 text-white py-2.5 px-4 rounded-lg text-sm font-medium hover:bg-purple-700 transition-colors block text-center">
                        Choose Developer
                    </a>
                </div>
            </div>

            <!-- Enterprise Plan -->
            <div class="pricing-card">
                <div class="text-center">
                    <h3 class="text-lg font-bold text-light mb-3">Enterprise</h3>
                    <div class="text-3xl font-bold text-light mb-2">$150</div>
                    <p class="text-sm text-muted mb-6">per month</p>
                    <ul class="text-left space-y-2 mb-6 text-sm">
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">10,000 user messages per month</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">All AI models</span></li>
                        <li class="flex items-center"><i class="fas fa-check text-success mr-2 text-xs"></i><span class="text-muted">Dedicated support</span></li>
                    </ul>
                    <a href="register.php" class="w-full bg-amber-600 text-white py-2.5 px-4 rounded-lg text-sm font-medium hover:bg-amber-700 transition-colors block text-center">
                        Choose Enterprise
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Models Section -->
<div class="py-20 bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-2xl md:text-3xl font-bold text-light mb-4">Supported AI Models</h2>
            <p class="text-lg text-muted max-w-2xl mx-auto">Access the latest and most powerful AI models from leading providers</p>
        </div>

        <div class="grid md:grid-cols-3 gap-6">
            <div class="model-card">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-blue-600 bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-brain text-blue-400"></i>
                    </div>
                    <h3 class="text-lg font-bold text-light">Claude Models</h3>
                </div>
                <ul class="space-y-2 text-sm">
                    <li class="flex items-center"><i class="fas fa-circle text-blue-400 mr-2 text-xs"></i><span class="text-muted">claude-3.5-sonnet</span></li>
                    <li class="flex items-center"><i class="fas fa-circle text-blue-400 mr-2 text-xs"></i><span class="text-muted">claude-3.7-sonnet</span></li>
                    <li class="flex items-center"><i class="fas fa-circle text-blue-400 mr-2 text-xs"></i><span class="text-muted">claude-4-sonnet</span></li>
                </ul>
            </div>

            <div class="model-card">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-green-600 bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-robot text-green-400"></i>
                    </div>
                    <h3 class="text-lg font-bold text-light">OpenAI Models</h3>
                </div>
                <ul class="space-y-2 text-sm">
                    <li class="flex items-center"><i class="fas fa-circle text-green-400 mr-2 text-xs"></i><span class="text-muted">o1</span></li>
                    <li class="flex items-center"><i class="fas fa-circle text-green-400 mr-2 text-xs"></i><span class="text-muted">o3-mini</span></li>
                    <li class="flex items-center"><i class="fas fa-circle text-green-400 mr-2 text-xs"></i><span class="text-muted">o4-mini</span></li>
                    <li class="flex items-center"><i class="fas fa-circle text-green-400 mr-2 text-xs"></i><span class="text-muted">gpt-4o-mini</span></li>
                </ul>
            </div>

            <div class="model-card">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-purple-600 bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-microchip text-purple-400"></i>
                    </div>
                    <h3 class="text-lg font-bold text-light">Other Models</h3>
                </div>
                <ul class="space-y-2 text-sm">
                    <li class="flex items-center"><i class="fas fa-circle text-purple-400 mr-2 text-xs"></i><span class="text-muted">qwen-2.5-72b-instruct-turbo</span></li>
                    <li class="flex items-center"><i class="fas fa-circle text-purple-400 mr-2 text-xs"></i><span class="text-muted">deepseek-r1</span></li>
                    <li class="flex items-center"><i class="fas fa-circle text-purple-400 mr-2 text-xs"></i><span class="text-muted">grok-3-beta</span></li>
                    <li class="flex items-center"><i class="fas fa-circle text-purple-400 mr-2 text-xs"></i><span class="text-muted">gemini-2.5-pro</span></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="bg-gray-900 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid md:grid-cols-4 gap-8">
            <div>
                <h3 class="text-lg font-bold mb-4 gradient-text">AI API Provider</h3>
                <p class="text-gray-400 text-sm leading-relaxed">Premium AI models at competitive prices with reliable infrastructure and excellent support.</p>
            </div>
            <div>
                <h4 class="font-semibold mb-4 text-sm text-light">Product</h4>
                <ul class="space-y-2 text-gray-400 text-sm">
                    <li><a href="docs.php" class="hover:text-white transition-colors">API Documentation</a></li>
                    <li><a href="#pricing" class="hover:text-white transition-colors">Pricing</a></li>
                    <li><a href="#models" class="hover:text-white transition-colors">Models</a></li>
                </ul>
            </div>
            <div>
                <h4 class="font-semibold mb-4 text-sm text-light">Support</h4>
                <ul class="space-y-2 text-gray-400 text-sm">
                    <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">Contact</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">Status</a></li>
                </ul>
            </div>
            <div>
                <h4 class="font-semibold mb-4 text-sm text-light">Company</h4>
                <ul class="space-y-2 text-gray-400 text-sm">
                    <li><a href="#" class="hover:text-white transition-colors">About</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">Terms</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">Privacy</a></li>
                </ul>
            </div>
        </div>
        <div class="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p class="text-sm">&copy; 2024 AI API Provider. All rights reserved.</p>
        </div>
    </div>
</footer>

<?php require_once 'includes/footer.php'; ?>
